import { createWithEqualityFn } from "zustand/traditional";
import { shallow } from "zustand/shallow";

const useStore = createWithEqualityFn(
  (set) => ({
    // Sound state and controls
    soundLevel: 10, // Initial sound volume
    setSoundLevel: (newValue) => set({ soundLevel: newValue }),

    isPlaying: false, // Play/pause state for audio
    togglePlayPause: () => set((state) => ({ isPlaying: !state.isPlaying })),

    soundControlIsVisible: false,
    toggleSoundControlVisibility: () =>
      set((state) => ({ soundControlIsVisible: !state.soundControlIsVisible })),

    // Cursor state
    cursorType: "pointer",
    updateCursorType: (newValue) => set({ cursorType: newValue }),

    // Active navigation button
    activeNav: "Home",
    updateActiveNav: (newValue) => set({ activeNav: newValue }),
  }),
  shallow // Ensures that state changes are only detected if values actually change
);

export { useStore };
