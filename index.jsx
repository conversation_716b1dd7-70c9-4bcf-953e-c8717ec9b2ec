import React from "react";
import { createRoot } from "react-dom/client";
import "./styles.css"; // Import des styles globaux
import App from "./App"; // Composant principal de votre application
import { ChatProvider } from "./hooks/useChat.jsx"; // Contexte de chat
import { AuthProvider } from "./AuthContext"; // Contexte pour l'authentification

// Récupération de l'élément HTML principal
const rootElement = document.getElementById("root");
if (!rootElement) {
  console.error("Impossible de trouver l'élément avec l'id 'root'. Assurez-vous que l'élément existe dans votre HTML.");
  throw new Error("Élément root introuvable");
}

// Création du root React
const root = createRoot(rootElement);

// Rendu de l'application encapsulée dans AuthProvider et ChatProvider
root.render(
  <React.StrictMode>
    <AuthProvider> {/* Fournit le contexte d'authentification à l'application */}
      <ChatProvider> {/* Fournit le contexte de chat à l'application */}
        <App /> {/* Composant principal */}
      </ChatProvider>
    </AuthProvider>
  </React.StrictMode>
);
