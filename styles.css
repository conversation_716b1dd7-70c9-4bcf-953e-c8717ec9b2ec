/* Global Body Styles */
body {
  margin: 0;
  padding: 0;
  font-family: 'Roboto-Black';
  background-color:#ffffff00;
  color: #000000;
  overflow-x: hidden;
  font-size: 1rem;
}


.styled-link-container {
  display: flex;
  gap: 5rem;
  justify-content: center; /* Centre le lien horizontalement */
  align-items: center; /* Centre le lien verticalement */
  padding: 40px;
}

.styled-link {
  background: linear-gradient(135deg, #d4af37, #b8860b); /* Gradient doré avec plus de contraste */
  border: none;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3); /* Ombre subtile pour plus de profondeur */
  color: #ffffff;
  color: #fff;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  margin: 0 auto;
  text-align: center;
  transition: background-color 0.3s ease;
  font-size: 1rem;
  font-weight: bold; /* Accentuation du texte */
  cursor: pointer;
  text-transform: uppercase; /* Style en majuscules pour les boutons */
  letter-spacing: 1px; /* Espacement léger entre les lettres */
}

.styled-link:hover {
  background-color: #e8b929ef; /* Changement de couleur au survol */
}
iframe {
  position: fixed;
  top: 0;
  left: 5rem; /* Adjusts for the sidebar width */
  width: calc(100% - 4rem); /* Occupies the remaining space after the sidebar */
  height: 100%; /* Full viewport height */
  border: none; /* Removes default iframe border */
  z-index: 1; /* Places iframe behind other elements if needed */
  cursor: default !important; /* Ensures a default cursor for the iframe */
  box-sizing: border-box; /* Ensures padding or borders don't affect dimensions */
  overflow: hidden; /* Hides scrollbars within the iframe */
  -ms-overflow-style: none; /* Hides scrollbars for Internet Explorer and Edge */
  scrollbar-width: none; /* Hides scrollbars for Firefox */
}

/* For browsers that need explicit scrollbar hiding */
iframe::-webkit-scrollbar {
  display: none; /* Hides scrollbars for Chrome, Safari, and newer Edge */
}



/* Fullscreen Container */
.fullscreen {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 5rem;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: #ffffff00;
}

/* Chat Container */
.chat-container {
  position: fixed;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center; /* Center horizontally */
  bottom: 0rem; /* Position the container 2rem above the bottom */
  transform: translateX(-60%); /* Center horizontally without affecting the bottom position */
  width: 40%;
  height: 100%; /* Adjust height dynamically based on content */
  max-height: 100%; /* Restrict maximum height to 50% of the viewport */
  background-color: #ffffff00; /* Optional: Add background color */
  overflow: hidden; /* Prevent content overflow */
  z-index: 20; /* Ensure the chat is above other content */
}
.purchase-credits-container {
  position: fixed;
  top: 0;
  left: 2em;
  width: calc(100% - 2em); /* Ajuster à la largeur restante */
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: flex-start; /* Pour que le contenu commence en haut */
  background: #fff;
  color: #000000;
  font-family: Arial, sans-serif;
  z-index: 1; /* Ensure it appears above other elements */
  overflow-y: auto; /* Permet le défilement vertical */
  padding: 1em; /* Ajouter un espacement autour */
}

/* Content box */
.purchase-credits-content {
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  border-radius: 10px;
  padding: 20px;
  max-height: 80%; /* Limiter la hauteur */
  max-width: 400px;
  width: 70%; /* Ajust to be responsive */
  text-align: center;
  overflow-y: auto; /* Défilement uniquement dans cette boîte */
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2); /* Ajout d'une ombre */
}

.credit-packs {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 20px 0;
}

.credit-pack {
  border: 2px solid #ddd;
  border-radius: 10px;
  padding: 10px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: 0.3s;
  width: 80%;
}

.credit-pack.selected {
  border-color: #0070f3;
  background-color: #f0f8ff;
}

.description {
  font-size: 0.9em;
  color: #555;
}

.success {
  color: green;
}

/* Error message styling */
.error {
  color: red;
  margin-bottom: 10px;
}







/* Chat Messages */
.chat-messages {
  position: fixed;
  top: 8rem;
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  max-height: 20%;
  width: 90%; /* Ensure messages take the full width */
  background-color: #ffffff00; /* Optional: Add a subtle background */
}
.joystick-container {
  position: fixed;
  bottom: 1rem;
  left: 50%;
  z-index: 10;
}

/* Individual Chat Messages */
.chat-message {
  margin-bottom: 2rem;
  padding: 1rem 1rem;
  font-size: 1.2rem;
  line-height: 1.2;
  max-width: 80%;
}

.chat-message.user {
  align-self: flex-end;
  background-color: #007bff23;
  color: white;
}

.chat-message.bot {
  align-self: flex-start;
  background-color: #f1f1f100;
  color: #ffffff;
  margin-bottom: 2rem;

}

/* Play Audio Button */
.play-audio-button {
  margin-top: 5px;
  padding: 5px 10px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.3s ease;
}

.play-audio-button:hover {
  background-color: #0056b3;
}

/* General error message styling */
.form-message {
  color: #d9534f; /* Bootstrap's danger red */
  font-size: 14px;
  margin-top: 8px;
  font-weight: bold;
  text-align: left;
}

/* Input error styling */
.input-error {
  border: 1px solid #d9534f; /* Red border for input errors */
  background-color: #f8d7da; /* Light red background */
  color: #721c24; /* Dark red text */
  box-shadow: 0 0 5px rgba(217, 83, 79, 0.5);
}

/* Error text under input */
.error-text {
  color: #d9534f; /* Bootstrap's danger red */
  font-size: 12px;
  margin-top: 4px;
  font-style: italic;
}

/* Button styling when errors are present */
button:disabled {
  background-color: #d9534f;
  color: #fff;
  cursor: not-allowed;
  opacity: 0.7;
}

/* Form group spacing */
.form-group {
  margin-bottom: 16px;
}

/* Chat Form */
.chat-form {
  display: flex;
  align-items: center;
  padding: 3rem;
  background-color: #ffffff00;
  width: 80%; /* Full width for form elements */
}

.chat-form input[type="text"] {
  flex: 1;
  padding: 1rem;
  font-size: 14px;
  border: 1px solid #e9ecef;
  border-radius: 5px;
  margin-right: 10px;
  margin-bottom: 2rem;
  outline: none;
  
}

.chat-form input[type="text"]:focus {
  border-color: #007bff;
}

.chat-form button {
  position: fixed ;
  left: 5rem;
  bottom: 0.5rem;
  padding: 10px 10px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
}

.chat-form button:disabled {
  background-color: #d6d6d6;
  cursor: not-allowed;
}

.chat-form button:hover:not(:disabled) {
  background-color: #0056b3;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-thumb {
  background-color: #cccccc;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #aaaaaa;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .chat-container {
    left: 50%;
    width: 90%; /* Adjust width for smaller screens */
    height: 100%; /* Dynamic height based on content */
    max-height: 100%; /* Limit height to 40% of viewport */
   
    bottom: 4rem; /* Closer to the bottom */
  }

  .chat-messages {
    padding: 2rem; /* Adjust padding for smaller screens */
    margin-bottom: 2rem;
  }

  .chat-form {
    margin-top: 15rem;
    flex-direction: column; /* Stack input and button vertically */
    padding: 1rem;
  }

  .chat-form input[type="text"] {
    margin-right: 0;
    margin-bottom: 3rem;
  }
}


/* Container for Timed Text */
.timed-text-container {
  display: flex;
  flex-direction: column; /* Stack elements vertically */
  align-items: center; /* Center content horizontally */
  justify-content: center; /* Center content vertically if needed */
  width: 100%; /* Full width of the parent or viewport */
  background-color: transparent; /* Ensure no visible background */
  padding: 1rem; /* Add some spacing inside the container */
  margin: 0 auto; /* Center the container within its parent */
  box-sizing: border-box; /* Ensure padding doesn't affect width */
}

/* Adjustments for full-width child elements */
.timed-text-container > * {
  width: 100%; /* Make child elements span full width */
  max-width: 1200px; /* Limit maximum width for better readability */
  margin: 0 auto; /* Center child elements */
}

/* Responsive Design Adjustments */
@media (max-width: 768px) {
  .timed-text-container {
    padding: 0.5rem; /* Reduce padding for smaller screens */
  }

  .timed-text-container > * {
    max-width: 95%; /* Slightly reduce max width */
  }
}

@media (max-width: 480px) {
  .timed-text-container {
    padding: 0.3rem; /* Further reduce padding */
  }

  .timed-text-container > * {
    max-width: 90%; /* Ensure better fit for very small screens */
  }
}


/* Separator */
.separator {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  margin: 0 10px;
  /* Adds spacing around the separator */
  text-align: center;
}

/* Loading Info */
.loading-info {
  color: #333;
  font-size: 1.4rem;
  text-align: center;
  margin-top: 0px;
  opacity: 0;
  animation: fadeIn 1s ease 1s forwards, pulseText 2s infinite ease-in-out;
}

/* Controls Container */
.controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
  flex-wrap: nowrap;
}

/* Audio Button Styling */
.audio-button {
  font-size: 1rem;
  font-weight: bold;
  background-color:#d4af37;
  color: #000000;
  cursor: pointer;
  border: none;
  padding: 0.5rem 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background-color 0.3s ease;
}

.audio-button.playing {
  background-color:#d4af37;
  color: #000000;
}

.audio-button:hover {
  background-color:#d4af37;
  color: #000000;
  transform: scale(1.1);
}




/* Playback Speed Controls */
.playback-speed-controls {
  display: flex;
  align-items: center;
  color: #000000;
  gap: 0.5rem;
}

.playback-speed-controls button {
  font-size: 2rem;
  padding: 1rem 1rem;
  background-color: #333;
  color: #000000;
  border: none;
  cursor: pointer;
  transition: transform 0.2s;
}

.playback-speed-controls button.active {
  background-color: #000000;
  color: #333;
}

.playback-speed-controls button:hover {
  transform: scale(1.1);
}

/* Loader Overlay */
.loader-overlay {
  position: relative;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  color: #000000;
  font-size: 24px;
  text-align: center;
}

/* Spinner Animation */
.loader-spinner {
  margin-right: 10px;
  columns: #121212;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #ffffff;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}





.play-controls-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 10px;
}



.playback-speed-controls {
  display: flex;
  justify-content: center;
  gap: 8px;
  /* Espace entre les boutons de vitesse */
}

.playback-speed-controls button {
  background-color: #ffffff00;
  color: #000000;
  padding: 0.3rem 1rem;
  font-size: 1rem;
  cursor: pointer;
  font-weight: 800;
  border: none;
  transition: background-color 0.3s ease;
}

.playback-speed-controls button.active {
  background-color: #333;
  color: #000000;
}

.playback-speed-controls button:hover {
  background-color: #333;
  color: #000000;
  text-shadow: 2px 2px 8px rgba(125, 113, 113, 0.8);
}


/* Container for mic button */
.mic-button-container {
  position: absolute;
  z-index: 15; /* Ensure visibility above other elements */
  flex-direction: column;
  left: 80%;
  padding: 0rem;
  bottom: 1rem;
  transform: translateX(-50%); /* Center horizontally on very small screens */
  background: rgba(255, 255, 255, 0); /* Subtle translucent background */
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0); /* Soft shadow */
  transition: all 0.3s ease; /* Smooth transitions for movement/hover */
}

/* Position adjustments for different screen sizes */
@media (min-width: 1024px) {
  .mic-button-container {
    left: 60%; /* Stay near the edge on large screens */
    bottom: 1rem; /* Consistent bottom positioning */
  }
}

@media (max-width: 1023px) {
  .mic-button-container {
    left: 75%;
    bottom: 2rem;
    padding: 0.8rem;
    gap: 10rem;
  }
}

@media (max-width: 767px) {
  .mic-button-container {
    left: 65%;
    bottom: 1rem;
    padding: 0.6rem;
    gap: 0.3rem;
  }
}

@media (max-width: 480px) {
  .mic-button-container {
    left: 85%;
    bottom: 5rem;
    transform: translateX(-50%); /* Center horizontally on very small screens */
    padding: 0rem;
    gap: 10rem;
  }
}

/* Mic button styles */
.mic-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px; /* Augmenter la taille sur PC */
  height: 60px; /* Augmenter la taille sur PC */
  border: none;
  border-radius: 50%; /* Circular shape */
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2); /* Subtle shadow for depth */
  color: #333333; /* Dark color for contrast */
  font-size: 30px; /* Augmenter la taille de la police */
  cursor: pointer;
  transition: all 0.3s ease; /* Smooth transitions for interactions */
}

.mic-icon {
  width: 40px; /* Agrandir l'icône */
  height: 40px; /* Agrandir l'icône */
  transition: all 0.3s ease; /* Smooth icon transitions */
}

/* Media queries for responsive adjustments */
@media (min-width: 1024px) {
  .mic-button {
    width: 70px; /* Plus grand sur écran large */
    height: 70px;
  }

  .mic-icon {
    width: 50px; /* Agrandir l'icône en proportion */
    height: 50px;
  }
}

@media (max-width: 1023px) {
  .mic-button {
    width: 60px; /* Taille intermédiaire */
    height: 60px;
  }

  .mic-icon {
    width: 40px;
    height: 40px;
  }
}

@media (max-width: 767px) {
  .mic-button {
    width: 50px; /* Taille plus petite pour les tablettes */
    height: 50px;
  }

  .mic-icon {
    width: 35px;
    height: 35px;
  }
}

@media (max-width: 480px) {
  .mic-button {
    width: 45px; /* Plus petit pour les très petits écrans */
    height: 45px;
  }

  .mic-icon {
    width: 30px;
    height: 30px;
  }
}

.mic-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #ffb700, #ffd700); /* Reverse gradient on hover */
  transform: scale(1.1); /* Slight zoom effect */
}

.mic-button:active {
  transform: scale(0.95); /* Slight press-in effect */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2); /* Reduced shadow on press */
}

.mic-button.active {
  background: linear-gradient(135deg, #ff4d4d, #ff1a1a); /* Active state with red gradient */
  box-shadow: 0 6px 20px rgba(255, 0, 0, 0.4); /* Highlighted shadow */
  transform: scale(1.15); /* Zoom in for emphasis */
  animation: pulse 1.5s infinite; /* Gentle pulsing effect */
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 10px rgba(255, 0, 0, 0.4);
  }
  50% {
    transform: scale(1.2);
    box-shadow: 0 0 20px rgba(255, 0, 0, 0.6);
  }
}

/* Disabled mic button */
.mic-button:disabled {
  background: #ccc; /* Neutral gray for disabled state */
  color: #666;
  cursor: not-allowed; /* Disable interactions */
  opacity: 0.7; /* Reduce visibility for disabled state */
  animation: none; /* No animation for disabled */
}

/* Mic icon */
.mic-icon {
  width: 32px; /* Larger icon size */
  height: 32px;
  transition: all 0.3s ease; /* Smooth icon transitions */
}

.mic-button-container .upload-indicator {
  color: #007bff; /* Bright blue for indicators */
  font-size: 14px;
  margin-top: 0.5rem;
}

/* Adjustments for accessibility and responsiveness */
.mic-button:focus {
  outline: 3px solid rgba(255, 215, 0, 0.8); /* Gold focus ring */
  outline-offset: 2px;
}






/* Avatar spacing */
.avatar-space {
  margin-top: 2rem;
  height: 80px;
  /* Define avatar size */
  width: 80px;
}

/* Error and success messages */
.error-text {
  color: #ff0000;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.success-text {
  color: #38a169;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}


/* 


.close-button {
  position: absolute;
  top: 0.2rem;
  border:none;
  right: 1rem;
  background: none;
  color: #ff0000;
  font-size: 0.8rem;
  cursor: pointer;
  font-family: 'Roboto-Black';
  transition: transform 0.3s ease, color 0.4s ease;
  pointer-events: auto;
  z-index: 100;
}
 */

/* Styles généraux */
.dashboard-fullscreen {
  position: absolute;
  top: 0;
  right: 0;
  width: 90%;
  height: 100vh;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-y: auto;
  padding: 1rem;
  box-sizing: border-box;
}

/* Conteneur principal */
.dashboard-container {
  position: relative;
  left: 1rem;
  width: 90%;
  height: 70vh;
  color: #000;
  max-width: 1200px;
  background: #ffffff;
  overflow-y: auto;
  max-height: 90vh;
  padding: 2rem;
}



/* Masquer les barres de défilement */
.dashboard-container {
  overflow: auto; /* Permet le défilement */
}

.dashboard-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.dashboard-container {
  -ms-overflow-style: none; /* Internet Explorer et Edge */
  scrollbar-width: none; /* Firefox */
}


/* Sections principales */
.user-info,
.user-progress,
.credit-logs,
.user-permissions {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  margin: 1.5rem 0;
  font-size: 1.1rem;
  text-align: left;
  padding: 1.5rem;
  border-radius: 15px;
  max-height: 300px;
  overflow-y: auto;
}

/* Barre de défilement des sections */
.user-info::-webkit-scrollbar,
.user-progress::-webkit-scrollbar,
.credit-logs::-webkit-scrollbar,
.user-permissions::-webkit-scrollbar {
  width: 8px;
}

.user-info::-webkit-scrollbar-thumb,
.user-progress::-webkit-scrollbar-thumb,
.credit-logs::-webkit-scrollbar-thumb,
.user-permissions::-webkit-scrollbar-thumb {
  background: #d4af37;
  border-radius: 8px;
}

.user-info::-webkit-scrollbar-track,
.user-progress::-webkit-scrollbar-track,
.credit-logs::-webkit-scrollbar-track,
.user-permissions::-webkit-scrollbar-track {
  background: #f9f9f9;
}

/* Ajout de marge et d'espacement interne */
.credit-logs ul {
  padding: 0.5rem;
  margin: 0;
  list-style: none;
}

.credit-logs li {
  margin-bottom: 1rem;
  padding: 0.5rem;
  border-bottom: 1px solid #ddd;
  font-size: 1rem;
}

/* Responsive pour écrans plus petits */
@media (max-width: 768px) {
  .user-info,
  .user-progress,
  .credit-logs,
  .user-permissions {
    max-height: 200px;
    padding: 1rem;
    font-size: 0.9rem;
  }
  .credit-logs li {
    font-size: 0.85rem;
  }
}

/* Conteneur principal */
.connection-time {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px;
  margin: 10px 0;
  background: linear-gradient(135deg, #e0f7fa, #ffffff);
  border: 1px solid #b2ebf2;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

/* Titre */
.connection-time h3 {
  margin: 0;
  font-size: 1.2rem;
  color: #00796b;
  font-weight: bold;
  text-transform: uppercase;
}

/* Texte de connexion */
.connection-time p {
  margin: 5px 0 0;
  font-size: 1rem;
  color: #004d40;
  font-weight: bold;
  letter-spacing: 0.5px;
}

/* Ajout de styles responsive */
@media (max-width: 768px) {
  .connection-time {
    padding: 10px;
  }

  .connection-time h3 {
    font-size: 1rem;
  }

  .connection-time p {
    font-size: 0.9rem;
  }
}


/* Styles généraux pour les boutons */
button {
  background: linear-gradient(135deg, #d4af37, #b8860b); /* Gradient doré avec plus de contraste */
  border: none;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3); /* Ombre subtile pour plus de profondeur */
  color: #ffffff;
  font-size: 1rem;
  font-weight: bold; /* Accentuation du texte */
  cursor: pointer;
  text-transform: uppercase; /* Style en majuscules pour les boutons */
  letter-spacing: 1px; /* Espacement léger entre les lettres */
}

button:hover {
  transform: translateY(-2px); /* Animation de soulèvement */
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4); /* Ombre accentuée */
  background: linear-gradient(135deg, #b8860b, #d4af37); /* Inversion du dégradé au survol */
}

button:active {
  transform: translateY(0); /* Retour à la position initiale */
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2); /* Ombre réduite pour indiquer une pression */
  background: linear-gradient(135deg, #a67c00, #b8860b); /* Couleurs assombries pour effet de pression */
}

/* Bouton spécifique : Déconnexion */
.logout-button {
  background: linear-gradient(135deg, #ff4d4d, #e60000); /* Dégradé rouge vif pour alerter l'utilisateur */
  font-size: 1rem;
  color: #ffffff;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  border: none;
  border-radius: 25px;
  padding: 10px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: auto; /* Centers the button horizontally */
}

/* Container for centering the button */
.logout-button-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%; /* Optional: Adjust based on container height */
  width: 100%; /* Optional: Adjust based on container width */
}

/* Hover effect */
.logout-button:hover {
  background: linear-gradient(135deg, #e60000, #ff4d4d); /* Inversion du dégradé au survol */
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4);
}

/* Active state */
.logout-button:active {
  background: linear-gradient(135deg, #cc0000, #b30000); /* Couleurs plus sombres à l'état actif */
  transform: translateY(0);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}


/* Bouton désactivé */
button:disabled {
  background: #cccccc; /* Gris clair pour état désactivé */
  color: #666666;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* Boutons responsives */
@media (max-width: 768px) {
  button {
    font-size: 0.8rem; /* Taille réduite pour écrans plus petits */
    padding: 15px 15px;
    
  }

  .logout-button {
    font-size: 0.9rem;
    padding: 8px 16px;
  }
}
/* Fullscreen container */
.lessons-fullscreen {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  padding: 1rem 1rem;
}

/* Main lessons container */
.lessons-container {
  width: 100%;
  max-width: 1200px;
  background-color: #ffffff;
  border-radius: 12px;
  padding: 2rem;
  overflow-y: auto; /* Keeps scrolling functionality */
  -ms-overflow-style: none; /* Hides scrollbar for IE and Edge */
  scrollbar-width: none; /* Hides scrollbar for Firefox */
}

.lessons-container::-webkit-scrollbar {
  display: none; /* Hides scrollbar for Chrome, Safari, and Edge */
}


/* Title styling */
.lessons-title {
  font-size: 2rem;
  font-weight: bold;
  text-align: center;
  color: #1a202c;
  margin-bottom: 1.5rem;
}

/* Lesson card styling */
.lesson-card {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  text-align: center; /* Center all content in the card */
}

/* Lesson image container */
.lesson-image-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

/* Lesson image */
.lesson-image {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.lesson-description {
  font-size: 1rem;
  line-height: 1.6;
  margin: 1rem 0;
  color: #4a5568;
  background-color: #f9fafb;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Phrase card styling */
.phrase-card {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center; /* Center content inside the phrase card */
}

/* Phrase image container */
.phrase-image-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

/* Phrase image */
.phrase-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.clickable-word {
  color: #1E90FF; /* Bright blue */
  font-weight: bold;
  cursor: pointer;
  transition: color 0.3s ease;
}

.clickable-word:hover {
  color: #0D47A1; /* Darker blue on hover */
  text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .lessons-container {
    padding: 1.5rem;
  }
  .lesson-card {
    padding: 1rem;
  }
  .lesson-image {
    max-width: 90%; /* Shrinks image on smaller screens */
  }
}

@media (max-width: 480px) {
  .lessons-container {
    padding: 1rem;
  }
  .lesson-card {
    padding: 0.8rem;
  }
  .lesson-image {
    max-width: 80%;
  }
}





/* Styles pour les éléments du menu */
.menu .menu-item {
  position: fic;
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px;
  font-size: 1.5rem;
  color:#d4af37;
 

}

/* Icône toujours visible */
.menu-item svg {
  position: relative;
  padding-left: -0.5rem;
  font-size: 2rem;
  color:#333;
  pointer-events: auto;
  /* S'assure que l'icône reste cliquable */
}



/* Custom Cursor */
.cursor {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 100%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 10;
}

.pointer {
  width: 25px;
  height: 25px;
  background: #ffffff;
  border: 2px solid #fff;
  z-index: 10;
}


.tabs-container {

  top: 0;
  left: 4rem;
  width: 100vw;
  height: 100vh;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-y: auto;
  padding: 0rem;
  z-index: 10;

}
.tabs-container {
  position: fixed;
  top: 0rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0rem;
}
#root > div > div > div.mic-button-container > div > div > form > button {
  position: relative;
  left: 35%;
  bottom: 2rem;
}

.header-navbar {
  z-index: 8;
}
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: #ffffff00;
  z-index: 1;
}

.navbar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
}

.toggle-btn {
  position: fixed;
  top: 1rem;
  left: 1rem;
  font-size: 1rem;
  background: none;
  border: none;
  cursor: pointer;
  outline: none;
  color: #333;
}

.toggle-btn:focus {
  outline: 2px solid #ffc107;
}

.navbar-menu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  height: 100%;
  background: #f8f9fa00;
  box-shadow: 0 4px 10px rgba(255, 255, 255, 0);
  padding: 1rem 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  z-index: 999;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 0.8rem 1.5rem;
  cursor: pointer;
  width: 100%;
  transition: background 0.3s, transform 0.2s;
}

.menu-item:hover {
  background: #ffc107;
  transform: translateX(10px);
}

.menu-item.active {
  background: #ffc107;
  color: #333;
  font-weight: bold;
}

.menu-text {
  margin-left: 0.8rem;
  font-size: 1.1rem;
  color: #000000;
}

.menu-item:hover .menu-text {
  color: #000;
}

.menu-item svg {
  font-size: 1.5rem;
  color:#333;
}

.menu-item:hover svg {
  color: #333;
}

@media (max-width: 768px) {
  .navbar-container {
    padding: 1rem;
  }

  .menu-text {
    font-size: 1rem;
  }
}
/* Synchronized Text */
.highlighted-text {
  width: 100%;
  /* Make the text take the full width */
  text-align: center;
  margin: 10px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Default Word Style */
.default-word {
  color: #000000;
  font-size: 1.2rem;
  font-weight: 600;
}

.highlighted-word {
  color: #ffe601;
  font-size: 1.2rem;
  font-weight: 600;
}
.highlighted-word {
  transition: color 0.3s ease, font-weight 0.3s ease;
}

.phrase.phrase-tab {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  margin-bottom: 0rem;
  /* Réduit la marge du bas pour chaque phrase */
  padding: 0rem;
  border-radius: 8px;
  background-color: #ffffff00;
}

.phrase-text {
  font-size: 1.5rem;
  color: #333;
  text-align: center;
  margin-bottom: 5px;
  /* Réduit l’espace entre le texte et les onglets */
}

.language-tabs {
  z-index: 1; /* Places it above other elements */

  display: flex;
  /* Active flexbox pour l'alignement */
  justify-content: center;
  /* Centre les onglets horizontalement */
  align-items: center;
  /* Centre les onglets verticalement */
  flex-direction: row;
  /* Aligne les onglets en ligne */
  gap: 1rem;
  /* Ajoute un espacement entre les onglets */
  margin: 1rem 0;
  /* Ajoute un espacement en haut et en bas */
  width: 100%;
  /* Assure que le conteneur prend toute la largeur */
  max-height: 8rem;
  position: relative;
  /* Si nécessaire pour le placement global */
}



.flag-icon {
  width: 32px;
  height: 32px;
}

.controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 10px;
}




.progress-bar {
  width: 80%;
  height: 20px;
  background-color: #0095ff;
  border-radius: 10px;
  overflow: hidden;
  margin-top: 20px;
  position: relative;
}



.hidden {
  display: none !important;
  visibility: hidden;
  width: 0;
  height: 0;
  overflow: hidden;
}

.section-overlay {
  position: fixed;
  bottom: 6rem;
  left: 0;
  width: 100vw;
  height: 50vh;
  background-color: #ffffff00;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
  padding-bottom: 1.5rem;
  /* Adds padding to inner content */
  pointer-events: auto;
  box-sizing: border-box;
}


.progress-bar-inner {
  height: 100%;
  background-color: #333;
  width: 0;
  transition: width 0.3s ease;
}

.progress-text {
  color: #0095ff;
  font-family: 'Roboto-Black';
  font-size: 1rem;
  font-weight: bold;
  position: absolute;
  right: 10px;
}

/* Social Section Styling */
.social-section {
  position: relative;
  top: 0.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  width: 70%;
  max-width: 1200px;
  margin: 0 auto;
  color: #333;
  font-family: 'Roboto-Black';
  text-align: center;
  background: #ffffff00;
  overflow-y: auto;
  /* Permet le défilement vertical */
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  max-height: 90vh;
  /* 80% de la hauteur de la fenêtre */
}
.h2 {
  font-size: 2rem;
  /* Slightly larger font for better prominence */
  color: #000000;
  /* Added a shadow for better visibility */
}

.social-section h2 {
  font-size: 2rem;
  /* Slightly larger font for better prominence */
  margin-bottom: 1rem;
  color: #333;
  text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4);
  /* Added a shadow for better visibility */
}

.social-section p {
  font-size: 1rem;
  /* Added a p tag styling */
  color: #ae233f99;
  margin-bottom: 2rem;
  line-height: 1;
}

.social-section a {
  color: #333;
  text-decoration: none;
  font-size: 1rem;
  margin: 5px 0;
  padding: 10px 15px;
  border: 2px solid #333;
  border-radius: 5px;
  transition: all 0.3s ease-in-out;
}

.social-section a:hover {
  color: #000;
  background-color: #333;
  border-color: #000000;
  transform: scale(1.1);
}

@media (max-width: 768px) {
  .social-section h2 {
    font-size: 1.5rem;
  }

  .social-section p {
    font-size: 1rem;
  }

  .social-section a {
    font-size: 1rem;
  }
}

/* Friends List Styling */
.friends-list {
  width: 100%;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;

}

.friends-list h3 {
  font-size: 1rem;
  margin-bottom: 1rem;
  color: #ffffff;
}

.friends-list ul {
  list-style: none;
  padding: 0;
}

.friends-list li {
  display: flex;
  justify-content: space-between;
  padding: 1.5rem;
  margin-bottom: 0.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  color: #333;
}

.friends-list button {
  background-color: #333;
  color: #000;
  font-weight: bold;
  padding: 1.3rem 0.6rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.friends-list button:hover {
  background-color: #333;
}

/* Add Friend Input */
.friends-list input[type="text"] {
  width: 80%;
  padding: 1.5rem;
  font-size: 1rem;
  border: none;
  border-radius: 5px;
  margin-right: 0.5rem;
}

.friends-list button {
  margin-top: 1rem;
  padding: 0.5rem;
  font-size: 1rem;
  background-color: #333;
  color: #000;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.friends-list button:hover {
  background-color: #333;
}

/* Create Post Styling */
.create-post {
  width: 100%;
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.create-post form {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.create-post textarea {
  width: 90%;
  height: 100px;
  padding: 0.5rem;
  font-size: 1rem;
  border: none;
  border-radius: 5px;
  margin-bottom: 1rem;
  resize: none;
  background: rgba(255, 255, 255, 0.8);
  color: #000;
}

.create-post button {
  padding: 0.5rem 1rem;
  font-size: 1.2rem;
  font-weight: bold;
  background-color: #333;
  color: #000;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.create-post button:hover {
  background-color: #333;
}

/* Post Feed Styling */
.post-feed {
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.post-feed .post {
  padding: 1rem;
  margin-bottom: 1rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: #ffffff;
  font-size: 1rem;
}

.post-feed .post p {
  margin: 0;
  font-size: 1.1rem;
}

.no-posts-message {
  text-align: center;
  color: #ffffff;
  font-size: 1.2rem;
  opacity: 0.8;
}

.canvas-visualizer {
  display: none;
}
.canvas-container {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden; /* No scrollbars */
  z-index: -1;
}

/* Language Selector */
.language-selector-container .language-flag {
  width: 30px;
  height: 20px;
  padding: 0rem;
}

.language-selector-container .react-select__control {
  justify-content: center;
  background-color: transparent;
  border: none;
  box-shadow: none;
  width: 60px;
}

.language-selector-container .react-select__menu {
  display: flex; /* Horizontal layout */
  flex-direction: row;
  background-color: #333;
  border: 1px solid #444;
  padding: 5px;
  z-index: 1000;
}

.language-selector-container .react-select__option {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  cursor: pointer;
  background-color: transparent;
}



/* Responsive Adjustments for Screen Sizes */

@media only screen and (max-width: 768px) {
  .controls {
    margin-top: 5px;
    /* Réduit l’espace entre les onglets et les contrôles */
    display: flex;
    flex-direction: column;
    align-items: center;
  }

}

@media (max-width: 768px) {
  .fullscreen {
    padding: 0;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
  }
}

@media (max-width: 480px) {
  .fullscreen {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
  }


}


/* Base Styling for All Input Fields */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="search"],
input[type="url"],
textarea {
  width: 75%; /* Full width */
  padding-bottom: 2rem;
  max-width: 300px; /* Restrict maximum width */
  padding: 1rem 1rem; /* Add space inside the input */
  font-size: 1rem; /* Make the text readable */
  border-radius: 8px; /* Rounded corners for a modern look */
  border: 2px solid rgba(0, 0, 0, 0.1); /* Light border */
  background-color: #f7f7f7; /* Subtle background for readability */
  color: #333; /* Dark text for contrast */
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1); /* Subtle inner shadow for depth */
  outline: none; /* Remove default outline */
  margin-bottom: 2rem;

}

/* Placeholder Styling */
input::placeholder,
textarea::placeholder {
  color: rgba(120, 120, 120, 0.8); /* Softer, italic placeholder */
  font-style: italic;
}

/* Hover Effect */
input:hover,
textarea:hover {
  border-color: #d4af37; /* Gold border on hover */
  background-color: #ffffff; /* Brighten background on hover */
}

/* Focus Effect */
input:focus,
textarea:focus {
  border-color: #d4af37; /* Highlight border with gold */
  background-color: #ffffff; /* White background */
  box-shadow: 0 0 8px rgba(212, 175, 55, 0.6); /* Subtle glow effect */
  transform: scale(1.02); /* Slight zoom effect */
}

/* Error State */
input.error,
textarea.error {
  border-color: #d9534f; /* Red border for error */
  background-color: #ffe6e6; /* Light red background */
}

/* Password Strength Indicator */
input.weak,
textarea.weak {
  border-color: #d9534f; /* Red border for weak password */
  background-color: #ffe6e6; /* Light red background */
}

input.medium,
textarea.medium {
  border-color: #f0ad4e; /* Orange border for medium password */
  background-color: #fff3e0; /* Light orange background */
}

input.strong,
textarea.strong {
  border-color: #5cb85c; /* Green border for strong password */
  background-color: #e9f7e9; /* Light green background */
}
/* styles.css */


.login-dialog-content {
  position: relative;
  right: 42%;
  bottom: 6rem;
  background-color: #fff;
  border-radius: 8px;
  padding: 1rem;
  width: 90%;
  max-width: 90%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  outline: none;
}

.login-dialog-title {
  font-size: 1.5rem;
  margin: 0 0 10px 0;
  color: #333;
  text-align: center;
}

.login-dialog-description {
  font-size: 1rem;
  color: #666;
  margin: 10px 0;
  text-align: center;
}

.login-dialog-button {
  display: block;
  background-color: #007bff;
  color: #fff;
  border: none;
  padding: 10px 20px;
  border-radius: 40px;
  cursor: pointer;
  font-size: 1rem;
  margin: 0 auto;
  text-align: center;
  transition: background-color 0.3s ease;
}

.login-dialog-button:hover {
  background-color: #0056b3;
}

.login-dialog-button:focus {
  outline: none;
  box-shadow: 0 0 4px #007bff;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  input,
  textarea {
    font-size: 0.9rem; /* Adjust font size for smaller screens */
    padding: 0.7rem 0.9rem; /* Reduce padding */
  }
}


/* Loading Info and Animation */
.loading-info {
  color: #333;
  font-size: 1.1rem;
  text-align: center;
  margin-top: 10px;
  opacity: 0;
  animation: fadeIn 1s ease-in-out 1s forwards, pulseText 2s infinite ease-in-out;
  font-family: 'Roboto-Black';
  transition: color 0.3s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes pulseText {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}



@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }
}

/* Introduction Text */
.intro-text {
  color: #333;
  font-size: 1rem;
  text-align: center;
  margin-top: 20px;
  opacity: 0;
  font-family: 'Roboto-Black';
  animation: fadeIn 2s ease 1.5s forwards;
  max-width: 85%;
  /* Limite la largeur pour éviter le débordement */
  line-height: 1.6;
  transition: color 0.3s ease;
}

/* Additional Responsive Tweaks for Mobile */
@media (max-width: 768px) {
  .play-btn {
    font-size: 1.2rem;
  }

  .loading-info,
  .intro-text {
    font-size: 1rem;
  }


}

@media (max-width: 480px) {
  .play-btn {
    font-size: 1.2rem;
  }

  .joystick-container {
    position: fixed;
    bottom: 1rem;
    left: 1rem;
    z-index: 10;
  }
 /* Chat Container */
.chat-container {
  position: fixed;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center; /* Center horizontally */
  bottom: 5rem; /* Position the container 2rem above the bottom */
  transform: translateX(-50%); /* Center horizontally without affecting the bottom position */
  width: 100%;
  height: 100%; /* Adjust height dynamically based on content */
  max-height: 100%; /* Restrict maximum height to 50% of the viewport */
  background-color: #00000000; /* Optional: Add background color */
  overflow: hidden; /* Prevent content overflow */
  z-index: 20; /* Ensure the chat is above other content */
}


  /* Additional styling adjustments for readability */
  .phrase-text {
    font-size: 1.2rem;
    color: #333;
    text-align: justify;
    margin-bottom: 0rem;
  }


  .controls {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-top: 10px;
  }






  .cursor {
    visibility: hidden;
  }

  .pointer {
    visibility: hidden;
  }

  /* Default Word Style */
  .default-word {
    color: #333;
    font-size: 1.2rem;
    font-weight: 900;
  }

  .highlighted-word {
    color: #0b0b0b;
    font-size: 1.2rem;
    font-weight: 900;
  }

  .text-pair {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }


  .highlighted-word,
  .highlighted-lesson-word {
    color: #d4af37;
    font-weight: 900;
    font-size: 1.2rem;
  }

  .default-word,
  .default-lesson-word {
    color: #0b0b0b;
    font-size: 1.2rem;
  }





  .default-word {
    color: #000000;
    font-size: 1.2rem;
    font-weight: 900;
  }



  .loading-info,
  .intro-text {
    font-size: 1rem;
  }


}