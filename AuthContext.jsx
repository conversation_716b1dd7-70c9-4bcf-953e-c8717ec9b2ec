import React, {
  createContext,
  useState,
  useEffect,
  useContext,
  useCallback,
} from 'react';
import axios from 'axios';




/* 
  -------------------------------------------------------------------------
  2. Configuration de l'API WordPress (JWT).
  -------------------------------------------------------------------------
*/
const WP_API_URL = import.meta.env.VITE_WP_API_URL || 'https://feelpolska.com/wp-json';

/**
 * Le plugin "JWT Authentication for WP-API" propose généralement :
 *    - POST /jwt-auth/v1/token          pour le login
 *    - POST /jwt-auth/v1/token/validate pour valider le token
 *    - GET  /wp/v2/users/me?context=edit  pour récupérer les infos de l’utilisateur
 * 
 * Pour les "crédits", on suppose avoir un endpoint custom 
 *    - GET  /custom-api/v1/user/credits
 * que vous devez créer vous-même dans WordPress.
 */

/* 
  -------------------------------------------------------------------------
  3. Création du contexte d'authentification
  -------------------------------------------------------------------------
*/
const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
  // ---------------------------------------------------------------------
  // État local pour stocker les infos utilisateur, crédits, etc.
  // ---------------------------------------------------------------------
  const [user, setUser] = useState(null);        // Informations de l'utilisateur WP
  const [loading, setLoading] = useState(true);  // Indicateur de chargement
  const [authError, setAuthError] = useState(null); // Message d'erreur d'authentification
  
  // ---------------------------------------------------------------------
  // Définir l'en-tête Authorization dans axios
  // ---------------------------------------------------------------------
  const setAuthHeaders = (token) => {
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  };

  // ---------------------------------------------------------------------
  // Réinitialiser l'état d'auth (utile pour logout)
  // ---------------------------------------------------------------------
  const resetAuthState = () => {
    localStorage.removeItem('token');
    delete axios.defaults.headers.common['Authorization'];
    setUser(null);
    setAuthError(null);
  };

  // ---------------------------------------------------------------------
  // Récupérer et valider les données de l'utilisateur via JWT
  // ---------------------------------------------------------------------
  const fetchUserData = useCallback(async () => {
    const token = localStorage.getItem('token');
    if (!token) {
      // Pas de token => pas connecté
      setLoading(false);
      return;
    }

    // On met le token dans les en-têtes
    setAuthHeaders(token);

    try {
      // 1) Valider le token via le plugin JWT
      const validate = await axios.post(`${WP_API_URL}/jwt-auth/v1/token/validate`);
      if (!(validate?.data?.data?.status === 200)) {
        throw new Error('Token invalide ou validation échouée');
      }

      // 2) Récupérer les infos de l'utilisateur
      const { data: userData } = await axios.get(`${WP_API_URL}/wp/v2/users/me?context=edit`);
      const { id, name } = userData; // "name" = user_display_name

      setUser({ id, username: name });
      setAuthError(null);

      // 3) Récupérer les crédits (endpoint custom)
    } catch (error) {
      console.error('Erreur de validation ou de récupération user:', error);
      resetAuthState();
      setAuthError('Session expirée. Veuillez vous reconnecter.');
    } finally {
      setLoading(false);
    }
  }, []);

  // Au montage du composant, on tente de récupérer les infos user
  useEffect(() => {
    fetchUserData();
  }, [fetchUserData]);

  // ---------------------------------------------------------------------
  // Récupération des "crédits" depuis un endpoint custom
  // ---------------------------------------------------------------------


  // ---------------------------------------------------------------------
  // Fonction de login (utilise /jwt-auth/v1/token)
  // ---------------------------------------------------------------------
  const login = async (username, password) => {
    try {
      setLoading(true);
      setAuthError(null);

      const { data } = await axios.post(`${WP_API_URL}/jwt-auth/v1/token`, {
        username,
        password,
      });

      const { token } = data;
      if (!token) {
        throw new Error('Le token JWT est manquant dans la réponse.');
      }

      // Stocker le token et mettre l'en-tête
      localStorage.setItem('token', token);
      setAuthHeaders(token);

      // Valider et récupérer les données utilisateur
      await fetchUserData();
    } catch (error) {
      console.error('Échec de la connexion:', error?.response || error.message || error);
      setAuthError(
        error?.response?.data?.message ||
          'Identifiants incorrects. Veuillez vérifier vos informations.'
      );
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // ---------------------------------------------------------------------
  // Fonction de logout
  // ---------------------------------------------------------------------
  const logout = () => {
    resetAuthState();
    setLoading(false);
  };

  // ---------------------------------------------------------------------
  // Forcer la récupération des infos (ex: pour rafraîchir crédits)
  // ---------------------------------------------------------------------
  const forceFetchUserData = () => {
    fetchUserData();
  };

  // ---------------------------------------------------------------------
  // Fournir les valeurs du contexte
  // ---------------------------------------------------------------------
  return (
    <AuthContext.Provider
      value={{
        user,        // { id, username }
        loading,     
        authError,   // Message d'erreur d'auth éventuel
        login,       // (username, password)
        logout,
        forceFetchUserData,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// ---------------------------------------------------------------------
// Hook custom: utilisation de l'AuthContext
// ---------------------------------------------------------------------
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth doit être utilisé dans AuthProvider');
  }
  return context;
};

export { AuthContext };
