import React, {
  useRef,
  useEffect,
  useCallback,
  useMemo,
  Suspense,
  useState,
} from "react";
import { Html, OrbitControls } from "@react-three/drei";
import { useThree, useFrame } from "@react-three/fiber";
import * as THREE from "three";
import PropTypes from "prop-types";
import Lights from "./components/Lights/Lights";
import Character from "./components/Character/Character";
import Karola from "./components/Karola/Karola";
import Loader from "./components/Loader/Loader";
import { Ocean } from './components/Ocean/Ocean';
import ChatDisplay from "./components/ChatDisplay/ChatDisplay";
import Island from "./components/Island/Island";

export function Experience({ joystickDirection }) {
  const audioListener = useRef(new THREE.AudioListener());
  const { camera, gl } = useThree();

  const refs = useRef({
    character: null,
    environment: null,
    karola: null,
  });

  const orbitControlsRef = useRef();
  const [isChatOpen, setIsChatOpen] = useState(false);

  const cameraOffsets = useMemo(
    () => ({
      behind: new THREE.Vector3(0, 15, -40),
      fps: new THREE.Vector3(0, 10, -25),
    }),
    []
  );

  useEffect(() => {
    camera.add(audioListener.current);
    return () => {
      camera.remove(audioListener.current);
    };
  }, [camera]);

  const updateCameraPosition = useCallback(() => {
    const { character } = refs.current;
    if (character) {
      const position = character.position?.clone() || new THREE.Vector3();
      const quaternion = character.quaternion?.clone() || new THREE.Quaternion();
      const isMoving = character.isMoving || false;

      const offset = isMoving ? cameraOffsets.behind : cameraOffsets.fps;
      const rotatedOffset = offset.clone().applyQuaternion(quaternion);
      const targetPosition = position.clone().add(rotatedOffset);

      camera.position.lerp(targetPosition, 0.15);

      const lookAtHeight = isMoving ? 1 : 8;
      camera.lookAt(position.clone().add(new THREE.Vector3(0, lookAtHeight, 0)));

      if (orbitControlsRef.current) {
        orbitControlsRef.current.enabled = !isMoving;
      }
    }
  }, [camera, cameraOffsets]);

  useFrame(updateCameraPosition);

  return (
    <Suspense fallback={<Html center><Loader /></Html>}>
      <OrbitControls
        ref={orbitControlsRef}
        args={[camera, gl.domElement]}

        rotateSpeed={1}
        minDistance={5}            // Minimum zoom-in distance
        maxDistance={3000000}           // Maximum zoom-out distance
      />


      <group ref={(ref) => (refs.current.environment = ref)}>
        <Lights />
        <Island position={[0, -36, 0]} scale={[10, 10, 10]} rotation={[0, Math.PI / 4, 0]} />
      </group>

      <group ref={(ref) => (refs.current.karola = ref)} position={[43, -12, 25]} scale={[1, 1, 1]} rotation={[0, 0.25, 0]}>
        <Karola />
      </group>

      <Character ref={(ref) => (refs.current.character = ref)} joystickDirection={joystickDirection} />
      <Ocean />

      {isChatOpen && (
        <Html
          position={[0, 5, 0]}
          style={{
            width: "300px",
            height: "400px",
            backgroundColor: "#fff",
            borderRadius: "10px",
            boxShadow: "0 4px 10px rgba(0,0,0,0.3)",
            overflow: "hidden",
          }}
        >
          <ChatDisplay onClose={() => setIsChatOpen(false)} />
        </Html>
      )}
    </Suspense>
  );
}

Experience.propTypes = {
  joystickDirection: PropTypes.string,
};

export default Experience;
