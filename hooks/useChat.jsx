import React, {
  createContext,
  useContext,
  useState,
  useMemo,
  useRef,
  useEffect,
  useCallback,
} from "react";

const backendUrl = "https://ia.hexadecaedre.com:8443";

const ChatContext = createContext();

export const ChatProvider = ({ children }) => {
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [cameraZoomed, setCameraZoomed] = useState(true);
  const [isAudioPlaying, setIsAudioPlaying] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(true);
  const [showLoginDialog, setShowLoginDialog] = useState(false);
  const [error, setError] = useState(null);

  const audioRef = useRef(null);

  // Fonction utilitaire pour logguer en dev
  const devLog = (...args) => {
    if (process.env.NODE_ENV !== "production") {
      console.log(...args);
    }
  };

  // Callback appelé après la fin de la lecture d'un message (audio)
  const onMessagePlayed = useCallback(() => {
    // Supprime le premier message de la liste une fois terminé
    setMessages((prevMessages) => {
      if (prevMessages.length > 0) {
        return prevMessages.slice(1);
      }
      return prevMessages;
    });
  }, []);

  useEffect(() => {
    // Initialisation de l'élément audio
    audioRef.current = new Audio();
    const audio = audioRef.current;

    const handleAudioEnd = () => {
      setIsAudioPlaying(false);
      onMessagePlayed(); // Passe au message suivant une fois l'audio terminé
    };

    const handleAudioError = (err) => {
      console.error("Audio playback error:", err);
      setIsAudioPlaying(false);
      onMessagePlayed(); // Passe au message suivant même en cas d'erreur
    };

    audio.addEventListener("ended", handleAudioEnd);
    audio.addEventListener("error", handleAudioError);

    return () => {
      audio.removeEventListener("ended", handleAudioEnd);
      audio.removeEventListener("error", handleAudioError);
    };
    // onMessagePlayed est un callback stable, pas besoin de le mettre en dépendance
    // pour ne pas ré-attacher sans cesse les écouteurs.
  }, [onMessagePlayed]);

  const playAudio = useCallback(
    (audioBase64) => {
      const audio = audioRef.current;
      if (!audio) {
        console.error("Audio element not initialized.");
        return;
      }

      if (isAudioPlaying) {
        devLog("Audio is already playing, new audio ignored.");
        return;
      }

      setIsAudioPlaying(true);
      audio.src = `data:audio/mp3;base64,${audioBase64}`;
      audio.load();

      audio.play().catch((err) => {
        console.error("Error playing audio:", err);
        setIsAudioPlaying(false);
        onMessagePlayed();
      });
    },
    [isAudioPlaying, onMessagePlayed]
  );

  /**
   * Traite le flux SSE renvoyé par le backend, ajoute les messages, gère les erreurs, etc.
   * @param {ReadableStreamDefaultReader<Uint8Array>} reader
   */
  const processSSEStream = useCallback(async (reader) => {
    const decoder = new TextDecoder("utf-8");
    let buffer = "";
    let done = false;

    while (!done) {
      const { value, done: streamDone } = await reader.read();
      if (streamDone) {
        done = true;
        break;
      }

      // Décodage en UTF-8
      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split("\n");
      buffer = lines.pop() || "";

      for (const line of lines) {
        if (line.startsWith("data: ")) {
          const jsonData = line.replace("data: ", "").trim();
          if (!jsonData) continue;

          let eventData;
          try {
            eventData = JSON.parse(jsonData);
          } catch (e) {
            console.error("Unable to parse SSE JSON:", e, jsonData);
            continue;
          }

          // Gestion des différents events SSE
          if (eventData.error) {
            console.error("Server error:", eventData.error);
            setError(eventData.error);

            // Si c'est un problème de crédits, on peut le logguer
            if (eventData.error === "Insufficient credits") {
              console.error(
                `Insufficient credits. Required: ${eventData.required}, Available: ${eventData.available}`
              );
            }
            // On arrête le traitement du flux
            done = true;
            break;
          } else if (eventData.status === "message") {
            // Ajoute un message à la file
            setMessages((prev) => [...prev, eventData.message]);
          } else if (eventData.status === "done") {
            // Tous les messages ont été envoyés
            devLog("SSE stream: done event received. Remaining credits:", eventData.credits);
            done = true;
            break;
          }
        }
      }
    }
  }, []);

  /**
   * Fonction pour envoyer un message au backend et traiter la réponse en temps réel (SSE)
   * @param {string} message - Le message utilisateur
   */
  const chat = useCallback(
    async (message) => {
      setLoading(true);
      setError(null);

      try {
        const token = localStorage.getItem("token");
        if (!token) {
          setIsAuthenticated(false);
          setShowLoginDialog(true);
          throw new Error("User not authenticated");
        }

        // Nettoyage des messages précédents
        setMessages([]);

        const response = await fetch(`${backendUrl}/chat`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({ message }),
        });

        if (response.status === 401) {
          // Token invalide ou expiré
          setIsAuthenticated(false);
          setShowLoginDialog(true);
          throw new Error("Invalid or expired token");
        }

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        // Lecture du flux SSE
        const reader = response.body.getReader();
        await processSSEStream(reader);

      } catch (err) {
        console.error("Error sending message:", err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    },
    [processSSEStream]
  );

  const contextValue = useMemo(
    () => ({
      chat,
      message: messages[0] || null, // Le message actuel, le premier de la liste
      messages,
      setMessages,
      onMessagePlayed,
      loading,
      cameraZoomed,
      setCameraZoomed,
      playAudio,
      isAuthenticated,
      showLoginDialog,
      setShowLoginDialog,
      error,
    }),
    [
      chat,
      messages,
      onMessagePlayed,
      loading,
      cameraZoomed,
      playAudio,
      isAuthenticated,
      showLoginDialog,
      error,
    ]
  );

  return <ChatContext.Provider value={contextValue}>{children}</ChatContext.Provider>;
};

export const useChat = () => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error("useChat must be used within a ChatProvider");
  }
  return context;
};