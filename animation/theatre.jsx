import studio from "@theatre/studio";
import extension from "@theatre/r3f/dist/extension";
import { getProject } from "@theatre/core";
import animationState from "./intro-island.json";

export function initTheatreStudio() {
  if (process.env.NODE_ENV === "development") {
    try {
      studio.initialize();
      studio.extend(extension);
      studio.ui.restore(); // Affiche l'interface Studio
      toggleTheatreUI(false); // Masque l'interface dès l'initialisation
      console.log("Theatre.js Studio initialized successfully.");
    } catch (error) {
      console.error("Erreur lors de l'initialisation de Theatre.js Studio :", error);
    }
  }
}


// Chargement du projet avec l'état d'animation
let project;
try {
  project = getProject("Intro Island", { state: animationState });
  console.log("Projet chargé :", project);
} catch (error) {
  console.error("Erreur lors de la création du projet Theatre.js :", error);
}

// Création et exportation des feuilles pour les animations spécifiques
export const cameraMovementSheet = project ? project.sheet("CameraMovement") : null;

// Fonction auxiliaire pour contrôler la visibilité de l'interface utilisateur
export function toggleTheatreUI(visible = false) {
  try {
    if (studio?.ui) {
      if (visible) {
        studio.ui.show();
      } else {
        studio.ui.hide();
      }
    } else {
      console.warn("Studio UI n'est pas disponible.");
    }
  } catch (error) {
    console.error("Erreur lors du contrôle de la visibilité de Theatre Studio UI :", error);
  }
}

// Réinitialisation de l'état du projet
export function resetProjectState(newState) {
  if (!project) {
    console.warn("Le projet Theatre.js n'est pas initialisé.");
    return;
  }

  try {
    project.importState(newState, { merge: true });
    console.log("État du projet réinitialisé.");
  } catch (error) {
    console.error("Erreur lors de la réinitialisation de l'état du projet :", error);
  }
}

// Fonction pour initialiser les lectures d'animation
export function initializeAnimationPlayback() {
  if (cameraMovementSheet?.sequence) {
    try {
      cameraMovementSheet.sequence.play({ loop: true });

      cameraMovementSheet.sequence.on("loop", () => {
        console.log("Animation en boucle");
      });
    } catch (error) {
      console.error("Erreur lors de la lecture de l'animation :", error);
    }
  } else {
    console.warn("Séquence d'animation introuvable ou non initialisée.");
  }
}

// Appel de l'initialisation en mode développement
initTheatreStudio();
