// axiosInstance.js
import axios from "axios";

const instance = axios.create({
  baseURL: "https://ia.hexadecaedre.com:8443",
  headers: {
    "Content-Type": "application/json"
  }
});

// Intercepteur pour ajouter le token d'authentification si présent
instance.interceptors.request.use((config) => {
  const token = localStorage.getItem("token");
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
}, (error) => {
  return Promise.reject(error);
});

export default instance;
