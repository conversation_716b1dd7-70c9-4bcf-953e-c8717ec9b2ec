import React, {
  useCallback,
  useMemo,
  useState,
  useContext,
  useRef,
} from "react";
import * as THREE from "three";
import { TextureLoader } from "three";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { Canvas } from "@react-three/fiber";
import { Physics } from "@react-three/cannon";
import Experience from "./Experience";
import { SheetProvider } from "@theatre/r3f";
import { cameraMovementSheet } from "./animation/theatre";
import MicButton from "./components/MicButton/MicButton";
import useMediaQuery from "./hooks/useMediaQuery";
import { shallow } from "zustand/shallow";
import { useStore } from "./store/store";
import { useChat } from "./hooks/useChat";
import Loader from "./components/Loader/Loader";
import { AuthProvider, AuthContext } from "./AuthContext";
import ProtectedRoute from "./components/ProtectedRoute/ProtectedRoute";
import UserDashboard from "./components/UserDashboard/UserDashboard";
import ChatDisplay from "./components/ChatDisplay/ChatDisplay";
import RegisterLogin from "./components/RegisterLogin/RegisterLogin";
import Lessons from "./components/Lessons/Lessons";
import PurchaseCredits from "./components/PurchaseCredits/PurchaseCredits";
import ExternalSite from "./components/Blog/Home";
import BlogSite from "./components/Blog/Blog";
import ShopSite from "./components/Blog/Shop";
import AccountSite from "./components/Blog/Account";
import MapSite from "./components/Blog/Map";
import { Joystick } from "react-joystick-component";
import { Leva } from "leva";

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error("Error caught in ErrorBoundary: ", error, errorInfo);
    this.setState({ error, errorInfo });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ color: "white", backgroundColor: "black", padding: "20px" }}>
          <h1>Something went wrong. Please try again later.</h1>
          <details style={{ whiteSpace: "pre-wrap" }}>
            {this.state.error && this.state.error.toString()}
            <br />
            {this.state.errorInfo?.componentStack || "No component stack available"}
          </details>
        </div>
      );
    }
    return this.props.children;
  }
}

function AppContent() {
  const [menuCollapse, setMenuCollapse] = useState(false);
  const [activeContent, setActiveContent] = useState("home");
  const [ready, setReady] = useState(false);
  const [isChatOpen, setIsChatOpen] = useState(true); // Chat open by default
  const [isPlaying, setIsPlaying] = useState(false);
  const audioRef = useRef(null);
  const [joystickDirection, setJoystickDirection] = useState(null);

  const { user, loading } = useContext(AuthContext);
  const userId = user?.id;
  const texture = useMemo(() => new TextureLoader().load("/field.jpg"), []);

  const [soundLevel, cursorType, updateCursorType, updateActiveNav] = useStore(
    (state) => [
      state.soundLevel,
      state.cursorType,
      state.updateCursorType,
      state.updateActiveNav,
    ],
    shallow
  );

  const isLargeScreen = useMediaQuery("(min-width: 769px)");
  const { chat, message, loading: chatLoading } = useChat();

  const handleMove = useCallback((e) => setJoystickDirection(e.direction), []);
  const handleStop = useCallback(() => setJoystickDirection(null), []);

  const stops = useMemo(() => [0, 2, 4, 6, 8, 10, 12, 14, 16], []);
  const sections = useMemo(
    () => [
      { stop: 2, section: "About" },
      { stop: 4, section: "Projects" },
    ],
    []
  );

  const handleClick = useCallback(() => {
    const nextStop = stops.find((stop) => stop > cameraMovementSheet.sequence.position);

    if (nextStop) {
      const currentSection = sections.find((section) => section.stop === nextStop)?.section;
      if (currentSection) {
        updateActiveNav(currentSection);
      }

      cameraMovementSheet.sequence.play({
        range: [cameraMovementSheet.sequence.position, nextStop],
        rate: 0.3,
      });
    } else {
      cameraMovementSheet.sequence.play({ range: [0, stops[0]], rate: 0.3 });
      updateActiveNav("Home");
    }
  }, [stops, sections, updateActiveNav]);

  const handleSendText = useCallback((transcribedText) => {
    chat(transcribedText);
  }, [chat]);

  const isDisabled = chatLoading || !!message;

  const toggleAudio = useCallback(() => {
    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
    setIsPlaying((prev) => !prev);
  }, [isPlaying]);

  if (loading) {
    return <Loader />;
  }

  return (
    <div style={{ display: "grid", height: "100vh", width: "100ù" , position: "relative" }}>
{/*       <Header
        toggleMenu={() => setMenuCollapse((prev) => !prev)}
        menuCollapse={menuCollapse}
        setActiveContent={setActiveContent}
      /> */}

      <div
/*         style={{
          flex: 1,
          transition: "margin-left 0.3s ease",
          marginLeft: menuCollapse ? "80px" : "50px",
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
        }} */
      >
        <Leva hidden />
        <audio ref={audioRef} loop src="/audio/birds.mp3" />

        <button
          onClick={toggleAudio}
          style={{
            position: "absolute",
            top: "20px",
            left: "50%",
            transform: "translateX(-50%)",
            padding: "10px 20px",
            backgroundColor: isPlaying ? "#f00" : "#0f0",
            color: "#fff",
            borderRadius: "5px",
            cursor: "pointer",
            zIndex: 100,
          }}
        >
          {isPlaying ? "Stop Audio" : "Play Audio"}
        </button>

        <Routes>
          <Route
            path="/"
            element={
              <SafeCanvas className="three-container" shadows>
                <SheetProvider sheet={cameraMovementSheet}>
                  <Experience
                    soundLevel={soundLevel}
                    ready={ready}
                    joystickDirection={joystickDirection}
                  />

                </SheetProvider>
              </SafeCanvas>
            }
          />
          <Route path="/dashboard" element={<ProtectedRoute component={UserDashboard} />} />
          <Route path="/login" element={<RegisterLogin />} />
          <Route path="/register-login" element={<RegisterLogin />} />
          <Route path="/external" element={<ExternalSite />} />
          <Route path="/blog" element={<BlogSite />} />
          <Route path="/shop" element={<ShopSite />} />
          <Route path="/map" element={<MapSite />} />
          <Route path="/account" element={<AccountSite />} />
          <Route path="/lessons" element={<Lessons />} />
          <Route path="/purchase-credits" element={<PurchaseCredits />} />
        </Routes>

        <MicButton onSend={handleSendText} disabled={isDisabled} userId={userId} />

        {isChatOpen && (
          <div
            style={{
              position: "absolute",
              bottom: "80px",
              right: "20px",
              width: "200px",
              height: "400px",
              zIndex: 10,
              backgroundColor: "#ffffff00",
              borderRadius: "10px",
              overflow: "hidden",
            }}
          >
            <ChatDisplay onClose={() => setIsChatOpen(false)} />
          </div>
        )}

        <button
          onClick={() => setIsChatOpen((prev) => !prev)}
          style={{
            position: "absolute",
            bottom: "20px",
            right: "20px",
            zIndex: 11,
            padding: "10px 20px",
            backgroundColor: "#333",
            color: "#fff",
            borderRadius: "5px",
            cursor: "pointer",
          }}
        >
          {isChatOpen ? "Close" : "Open"}
        </button>

        <div className="joystick-container">
          <Joystick size={50} baseColor="gray" stickColor="#d4af37" move={handleMove} stop={handleStop} />
        </div>
      </div>
    </div>
  );
}

export default function App() {
  return (
    <AuthProvider>
      <ErrorBoundary>
        <Router>
          <AppContent />
        </Router>
      </ErrorBoundary>
    </AuthProvider>
  );
}

const SafeCanvas = ({ children, ...props }) => (
  <Canvas
    gl={{
      alpha: true,
      antialias: true,
      powerPreference: "default",
    }}
    camera={{
      fov: 55,
      near: 0.1,
      far: 50000,       // Étendre la portée de la caméra
      position: [0, 50, 150], // Reculer légèrement la caméra
    }}
    frameloop="demand"
    {...props}
  >
    <Physics gravity={[0, -9.81, 0]}>{children}</Physics>
  </Canvas>
);
