import { Text3D, useTexture } from "@react-three/drei";
import { memo, useRef, useState } from "react";
import { useFrame } from "@react-three/fiber";
import * as THREE from "three";

/** Displaying 3D text with water-like effect on hover */
export const CustomText3D = memo(function CustomText3D({
  text = "Enter text as param",
  position = [5, 28, 12],
  rotation = [0, 1.6, 0],
  size = 20,
  height = 0.1,
  curveSegments = 10,
  bevelEnabled = true,
  bevelThickness = 0.5,
  bevelSize = 0.2,
  bevelOffset = 0.01,
  bevelSegments = 5,
  letterSpacing = 0.5,
  opacity = 1,
  ...props
}) {
  // Load the matcap texture for the text
  const matcapTexture = useTexture("./images/matcap.png");

  // Refs for geometry and mesh
  const meshRef = useRef();
  const [hovered, setHovered] = useState(false);

  // State for mouse position
  const [mousePos, setMousePos] = useState(new THREE.Vector2(0, 0));

  // Handler for mouse move event
  const onMouseMove = (e) => {
    const { offsetX, offsetY } = e.nativeEvent;
    setMousePos(
      new THREE.Vector2(
        (offsetX / window.innerWidth) * 2 - 1,
        -(offsetY / window.innerHeight) * 2 + 1
      )
    );
  };

  // Apply hover effect using useFrame
  useFrame((state) => {
    if (hovered && meshRef.current) {
      const time = state.clock.getElapsedTime();
      const geometry = meshRef.current.geometry;
      const positionAttribute = geometry.attributes.position;
      const vertexCount = positionAttribute.count;

      // Clone the original positions if not already done
      if (!geometry.originalPosition) {
        geometry.originalPosition = positionAttribute.array.slice();
      }

      for (let i = 0; i < vertexCount; i++) {
        const originalX = geometry.originalPosition[i * 3];
        const originalY = geometry.originalPosition[i * 3 + 1];
        const originalZ = geometry.originalPosition[i * 3 + 2];

        const vertex = new THREE.Vector3(originalX, originalY, originalZ);

        const distance = vertex.distanceTo(new THREE.Vector3(mousePos.x, mousePos.y, 0));
        const factor = Math.exp(-distance * 10); // Influence factor
        vertex.z += Math.sin(time + i) * 0.1 * factor;

        // Update the position attribute
        positionAttribute.setXYZ(i, vertex.x, vertex.y, vertex.z);
      }

      positionAttribute.needsUpdate = true;
    }
  });

  return (
    <mesh
      ref={meshRef}
      position={position}
      rotation={rotation}
      onPointerOver={() => setHovered(true)}
      onPointerOut={() => setHovered(false)}
      onPointerMove={onMouseMove}
    >
      <Text3D
        font="./fonts/Bebas Neue_Regular.json"
        size={size}
        height={height}
        curveSegments={curveSegments}
        bevelEnabled={bevelEnabled}
        bevelThickness={bevelThickness}
        bevelSize={bevelSize}
        bevelOffset={bevelOffset}
        bevelSegments={bevelSegments}
        letterSpacing={letterSpacing}
        {...props}
      >
        {text}
        <meshMatcapMaterial
          matcap={matcapTexture}
          transparent={opacity < 1}
          opacity={opacity}
          wireframe={false}
        />
      </Text3D>
    </mesh>
  );
});

export default CustomText3D;
