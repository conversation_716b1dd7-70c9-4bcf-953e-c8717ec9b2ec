import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  DirectionalLightHelper,
} from "three"; // Importing light helpers from the Three.js library
import { useHelper } from "@react-three/drei"; // Importing a utility for adding helpers to lights

const LightSetup = () => {
  // Creating references for the lights
  const spotLightRef = React.useRef();
  const pointLightRef = React.useRef();
  const hemisphereLightRef = React.useRef();
  const directionalLightRef = React.useRef();

  /* // Attaching helpers to visualize the lights in the scene 
  useHelper(spotLightRef, SpotLightHelper, "cyan"); // Visualize SpotLight with a cyan helper
  useHelper(pointLightRef, PointLightHelper, 0.5); // Visualize PointLight with a helper of size 0.5
  useHelper(hemisphereLightRef, HemisphereLightHelper, 1); // Visualize HemisphereLight with a helper
  useHelper(directionalLightRef, DirectionalLightHelper, 1); // Visualize DirectionalLight with a helper
 */
  return (
    <>
      {/* Directional light for the main lighting in the scene */}
      <directionalLight
        ref={directionalLightRef} // Attaching the reference for the helper
        intensity={1.5} // Brightness of the light
        position={[10, 15, 10]} // Position of the light source
        shadow-mapSize={[2048, 2048]} // Resolution of the shadow map
        castShadow // Enabling shadows for this light
        color="#ffffff" // White light color
      />

      {/* SpotLight for focused lighting effects with a helper */}
      <spotLight
        ref={spotLightRef} // Attaching the reference for the helper
        position={[0, 8, 15]} // Position of the SpotLight
        angle={0.4} // Beam angle of the SpotLight
        penumbra={0.5} // Softness of the SpotLight edges
        intensity={1.8} // Brightness of the light
        castShadow // Enabling shadows for this light
        shadow-bias={-0.0001} // Reducing shadow artifacts
        color="#ffddaa" // Warm light color
      />

      {/* PointLight for ambient or localized soft lighting with a helper */}
      <pointLight
        ref={pointLightRef} // Attaching the reference for the helper
        position={[-8, 5, 3]} // Position of the PointLight
        intensity={1.2} // Brightness of the light
        distance={20} // Maximum range of the light
        decay={2} // Light intensity decay over distance
        color="#aaffcc" // Soft greenish light color
      />

      {/* HemisphereLight for balanced ambient lighting */}
      <hemisphereLight
        ref={hemisphereLightRef} // Attaching the reference for the helper
        intensity={0.9} // Brightness of the light
        skyColor="#87ceeb" // Sky color for the upper hemisphere (light blue)
        groundColor="#ffe4b5" // Ground color for the lower hemisphere (beige)
        position={[0, 10, 0]} // Position of the light source
      />
    </>
  );
};

export default LightSetup; // Exporting the component
