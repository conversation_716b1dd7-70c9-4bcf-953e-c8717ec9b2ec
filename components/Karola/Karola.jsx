import { useAnimations, useGLTF } from "@react-three/drei";
import { useFrame } from "@react-three/fiber";
import React, { useEffect, useRef, useState, forwardRef, useImperativeHandle } from "react";
import * as THREE from "three";
import { useChat } from "../../hooks/useChat.jsx";
import { button, useControls } from "leva";

// Predefined facial expressions for the avatar
const facialExpressions = {
  default: {},
  smile: {
    browInnerUp: 0.17,
    eyeSquintLeft: 0.4,
    eyeSquintRight: 0.44,
    noseSneerLeft: 0.17,
    noseSneerRight: 0.14,
    mouthPressLeft: 0.61,
    mouthPressRight: 0.41,
  },
  funnyFace: {
    jawLeft: 0.63,
    mouthPucker: 0.53,
    noseSneerLeft: 1,
    noseSneerRight: 0.39,
    mouthLeft: 1,
    eyeLookUpLeft: 1,
    eyeLookUpRight: 1,
    cheekPuff: 1,
    mouthDimpleLeft: 0.41,
    mouthRollLower: 0.32,
    mouthSmileLeft: 0.35,
    mouthSmileRight: 0.35,
  },
  sad: {
    mouthFrownLeft: 1,
    mouthFrownRight: 1,
    mouthShrugLower: 0.78,
    browInnerUp: 0.45,
    eyeSquintLeft: 0.72,
    eyeSquintRight: 0.75,
    eyeLookDownLeft: 0.5,
    eyeLookDownRight: 0.5,
    jawForward: 1,
  },
  surprised: {
    browInnerUp: 0.8,
    eyeWideLeft: 0.5,
    eyeWideRight: 0.5,
    jawOpen: 0.35,
    mouthFunnel: 0.34,
  },
  angry: {
    browDownLeft: 1,
    browDownRight: 1,
    eyeSquintLeft: 1,
    eyeSquintRight: 1,
    jawForward: 1,
    jawLeft: 1,
    mouthShrugLower: 1,
    noseSneerLeft: 1,
    noseSneerRight: 0.42,
    eyeLookDownLeft: 0.16,
    eyeLookDownRight: 0.16,
    cheekSquintLeft: 1,
    cheekSquintRight: 1,
    mouthClose: 0.23,
    mouthFunnel: 0.63,
    mouthDimpleRight: 1,
  },
  crazy: {
    browInnerUp: 0.9,
    jawForward: 1,
    noseSneerLeft: 0.57,
    noseSneerRight: 0.51,
    eyeLookDownLeft: 0.39,
    eyeLookUpRight: 0.4,
    eyeLookInLeft: 0.96,
    eyeLookInRight: 0.96,
    jawOpen: 0.96,
    mouthDimpleLeft: 0.96,
    mouthDimpleRight: 0.96,
    mouthStretchLeft: 0.28,
    mouthStretchRight: 0.29,
    mouthSmileLeft: 0.56,
    mouthSmileRight: 0.38,
    tongueOut: 0.96,
  },
};

// Mapping of phonemes to avatar mouth shapes for lipsync
const corresponding = {
  A: "viseme_PP",
  B: "viseme_kk",
  C: "viseme_I",
  D: "viseme_AA",
  E: "viseme_O",
  F: "viseme_U",
  G: "viseme_FF",
  H: "viseme_TH",
  X: "viseme_PP",
};


let setupMode = false;

// Main Avatar Component
const Karola = forwardRef((props, ref) => {
  const { nodes, materials } = useGLTF("/models/karola.glb", true);
  const { animations } = useGLTF("/models/animation.glb", true);
  const group = useRef();
  const { message, onMessagePlayed } = useChat();
  const { actions } = useAnimations(animations, group);

  const [animation, setAnimation] = useState("Idle");
  const [audio, setAudio] = useState(null);
  const [lipsync, setLipsync] = useState(null);

  // État pour gérer une transition fluide de position
  const [targetPosition, setTargetPosition] = useState(new THREE.Vector3(0, -24, 0));
  const [blink, setBlink] = useState(false);
  const [winkLeft, setWinkLeft] = useState(false);
  const [winkRight, setWinkRight] = useState(false);
  const [facialExpression, setFacialExpression] = useState("");

  // Gestion des expressions faciales et du lipsync
  useFrame(() => {
    if (!setupMode) {
      Object.keys(nodes.EyeLeft.morphTargetDictionary).forEach((key) => {
        const mapping = facialExpressions[facialExpression];
        if (key === "eyeBlinkLeft" || key === "eyeBlinkRight") return;
        if (mapping && mapping[key]) {
          lerpMorphTarget(key, mapping[key], 0.1);
        } else {
          lerpMorphTarget(key, 0, 0.1);
        }
      });

      lerpMorphTarget("eyeBlinkLeft", blink || winkLeft ? 1 : 0, 0.5);
      lerpMorphTarget("eyeBlinkRight", blink || winkRight ? 1 : 0, 0.5);
    }

    if (setupMode) return;

    const appliedMorphTargets = [];
    if (message && lipsync) {
      const currentAudioTime = audio.currentTime;
      lipsync.mouthCues.forEach((mouthCue) => {
        if (currentAudioTime >= mouthCue.start && currentAudioTime <= mouthCue.end) {
          appliedMorphTargets.push(corresponding[mouthCue.value]);
          lerpMorphTarget(corresponding[mouthCue.value], 1, 0.2);
        }
      });
    }

    Object.values(corresponding).forEach((value) => {
      if (!appliedMorphTargets.includes(value)) {
        lerpMorphTarget(value, 0, 0.1);
      }
    });
  });



  // Expose des fonctions au parent
  useImperativeHandle(ref, () => ({
    startLipSync: (speechMarks, audioContent, callback) => {
      // Arrêter l'audio précédent s'il existe
      if (audio) {
        audio.pause();
        audio.currentTime = 0;
      }

      // Initialiser un nouvel audio
      const newAudio = new Audio(audioContent);
      setAudio(newAudio);

      // Définir les nouveaux phonèmes
      setLipsync({ mouthCues: speechMarks });

      // Démarrer l'audio et gérer sa fin
      newAudio.play().catch((err) => console.error("Audio playback error:", err));
      newAudio.onended = () => {
        setLipsync(null); // Réinitialiser l’état lipsync
        closeMouth(); // Fermer la bouche après le message
        setFacialExpression("default");
        setAnimation("Idle");
        callback?.();
      };
    },
  }));

  // Fonction pour fermer la bouche
  const closeMouth = () => {
    Object.values(corresponding).forEach((target) =>
      lerpMorphTarget(target, 0, 0.2)
    );
  };

  // Gestion des animations
  useEffect(() => {
    if (actions && actions[animation]) {
      actions[animation].reset().fadeIn(0.5).play();
      return () => actions[animation]?.fadeOut(0.5);
    }
  }, [animation, actions]);

  // Gestion des messages entrants
  useEffect(() => {
    if (message) {
      setAnimation(message.animation || "Idle");
      setFacialExpression(message.facialExpression || "default");
      setLipsync(message.lipsync);

      // Transition fluide vers une nouvelle position
      setTargetPosition(new THREE.Vector3(0, -24 + Math.random() * 2 - 1, 0));

      // Jouer l'audio du message
      const audioInstance = new Audio("data:audio/mp3;base64," + message.audio);
      setAudio(audioInstance);
      audioInstance.play().catch((err) =>
        console.error("Error playing message audio:", err)
      );

      audioInstance.onended = () => {
        onMessagePlayed();
        closeMouth();
        setFacialExpression("default");
        setAnimation("Idle");
        setLipsync(null);
      };
    }
  }, [message, onMessagePlayed]);

  // Interpolation des morph targets
  const lerpMorphTarget = (target, value, speed = 0.1) => {
    group.current.traverse((child) => {
      if (child.isSkinnedMesh && child.morphTargetDictionary) {
        const index = child.morphTargetDictionary[target];
        if (index !== undefined) {
          child.morphTargetInfluences[index] = THREE.MathUtils.lerp(
            child.morphTargetInfluences[index],
            value,
            speed
          );
        }
      }
    });
  };

  // Mise à jour des phonèmes et expressions faciales
  useFrame((_, delta) => {
    // Expressions faciales
    const expression = facialExpressions[facialExpression];
    Object.keys(expression).forEach((key) => {
      lerpMorphTarget(key, expression[key], 0.1);
    });

    // Synchronisation des phonèmes
    if (lipsync && audio) {
      const currentTime = audio.currentTime;

      // Trouver les phonèmes actifs
      const activePhonemes = lipsync.mouthCues.filter(
        (cue) => currentTime >= cue.start && currentTime <= cue.end
      );

      // Activer les targets correspondantes
      const activeTargets = new Set(
        activePhonemes.map((cue) => corresponding[cue.value])
      );

      activeTargets.forEach((target) => {
        if (target) lerpMorphTarget(target, 1, 0.2);
      });

      // Réinitialiser les morph targets non utilisées
      Object.values(corresponding).forEach((target) => {
        if (!activeTargets.has(target)) {
          lerpMorphTarget(target, 0, 0.1);
        }
      });
    }

    // Transition fluide de la position
    group.current.position.lerp(targetPosition, delta * 0.5);
  });

  // Nettoyage de l'état
  useEffect(() => {
    return () => {
      if (audio) {
        audio.pause();
        audio.currentTime = 0;
      }
    };
  }, [audio]);
  // Contrôles pour les expressions faciales et les animations
  useControls("FacialExpressions", {
    chat: button(() => chat()),
    winkLeft: button(() => {
      setWinkLeft(true);
      setTimeout(() => setWinkLeft(false), 300);
    }),
    winkRight: button(() => {
      setWinkRight(true);
      setTimeout(() => setWinkRight(false), 300);
    }),
    animation: {
      value: animation,
      options: animations.map((a) => a.name),
      onChange: (value) => setAnimation(value),
    },
    facialExpression: {
      options: Object.keys(facialExpressions),
      onChange: (value) => setFacialExpression(value),
    },
    enableSetupMode: button(() => {
      setupMode = true;
    }),
    disableSetupMode: button(() => {
      setupMode = false;
    }),
    logMorphTargetValues: button(() => {
      const emotionValues = {};
      Object.keys(nodes.EyeLeft.morphTargetDictionary).forEach((key) => {
        if (key === "eyeBlinkLeft" || key === "eyeBlinkRight") return;
        const value = nodes.EyeLeft.morphTargetInfluences[nodes.EyeLeft.morphTargetDictionary[key]];
        if (value > 0.01) emotionValues[key] = value;
      });
      console.log(JSON.stringify(emotionValues, null, 2));
    }),
  });

  useControls("MorphTarget", () =>
    Object.assign(
      {},
      ...Object.keys(nodes.EyeLeft.morphTargetDictionary).map((key) => {
        return {
          [key]: {
            label: key,
            value: 0,
            min: nodes.EyeLeft.morphTargetInfluences[nodes.EyeLeft.morphTargetDictionary[key]],
            max: 1,
            onChange: (val) => {
              if (setupMode) lerpMorphTarget(key, val, 1);
            },
          },
        };
      })
    )
  );

  // Gestion des clignements des yeux
  useEffect(() => {
    let blinkTimeout;
    const nextBlink = () => {
      blinkTimeout = setTimeout(() => {
        setBlink(true);
        setTimeout(() => {
          setBlink(false);
          nextBlink();
        }, 200);
      }, THREE.MathUtils.randInt(1000, 5000));
    };
    nextBlink();
    return () => clearTimeout(blinkTimeout);
  }, []);

  // Rendu du composant
  return (
    <group
      {...props}
      dispose={null}
      ref={group}
      scale={[5, 5, 5]}
      position={[0, -24, 0]}
      rotation={[0, 2.8, 0]}
    >
      {/* Environment lighting setup */}
      <ambientLight intensity={0.5} color="#ffffff" />


      {/* SpotLight for focused lighting effects with a helper */}
      <spotLight
        position={[0, 5, 15]} // Position of the SpotLight
        angle={0.4} // Beam angle of the SpotLight
        penumbra={0.5} // Softness of the SpotLight edges
        intensity={1.8} // Brightness of the light
        castShadow // Enabling shadows for this light
        shadow-bias={-0.0001} // Reducing shadow artifacts
        color="#ffddaa" // Warm light color
      />

      {/* PointLight for ambient or localized soft lighting with a helper */}
      <pointLight

        position={[-8, 5, 3]} // Position of the PointLight
        intensity={1.2} // Brightness of the light
        distance={2} // Maximum range of the light
        decay={2} // Light intensity decay over distance
        color="#aaffcc" // Soft greenish light color
      />

      {/* HemisphereLight for balanced ambient lighting */}
      <hemisphereLight

        intensity={0.9} // Brightness of the light
        skyColor="#87ceeb" // Sky color for the upper hemisphere (light blue)
        groundColor="#ffe4b5" // Ground color for the lower hemisphere (beige)
        position={[0, 10, 0]} // Position of the light source
      />
      <primitive object={nodes.Hips} />
      <skinnedMesh
        name="Wolf3D_Body"
        geometry={nodes.Wolf3D_Body.geometry}
        material={materials.Wolf3D_Body}
        skeleton={nodes.Wolf3D_Body.skeleton}
      />
      <skinnedMesh
        name="Wolf3D_Outfit_Bottom"
        geometry={nodes.Wolf3D_Outfit_Bottom.geometry}
        material={materials.Wolf3D_Outfit_Bottom}
        skeleton={nodes.Wolf3D_Outfit_Bottom.skeleton}
      />
      <skinnedMesh
        name="Wolf3D_Outfit_Footwear"
        geometry={nodes.Wolf3D_Outfit_Footwear.geometry}
        material={materials.Wolf3D_Outfit_Footwear}
        skeleton={nodes.Wolf3D_Outfit_Footwear.skeleton}
      />
      <skinnedMesh
        name="Wolf3D_Outfit_Top"
        geometry={nodes.Wolf3D_Outfit_Top.geometry}
        material={materials.Wolf3D_Outfit_Top}
        skeleton={nodes.Wolf3D_Outfit_Top.skeleton}
      />
      <skinnedMesh
        name="Wolf3D_Hair"
        geometry={nodes.Wolf3D_Hair.geometry}
        material={materials.Wolf3D_Hair}
        skeleton={nodes.Wolf3D_Hair.skeleton}
      />
      <skinnedMesh
        name="EyeLeft"
        geometry={nodes.EyeLeft.geometry}
        material={materials.Wolf3D_Eye}
        skeleton={nodes.EyeLeft.skeleton}
        morphTargetDictionary={nodes.EyeLeft.morphTargetDictionary}
        morphTargetInfluences={nodes.EyeLeft.morphTargetInfluences}
      />
      <skinnedMesh
        name="EyeRight"
        geometry={nodes.EyeRight.geometry}
        material={materials.Wolf3D_Eye}
        skeleton={nodes.EyeRight.skeleton}
        morphTargetDictionary={nodes.EyeRight.morphTargetDictionary}
        morphTargetInfluences={nodes.EyeRight.morphTargetInfluences}
      />
      <skinnedMesh
        name="Wolf3D_Head"
        geometry={nodes.Wolf3D_Head.geometry}
        material={materials.Wolf3D_Skin}
        skeleton={nodes.Wolf3D_Head.skeleton}
        morphTargetDictionary={nodes.Wolf3D_Head.morphTargetDictionary}
        morphTargetInfluences={nodes.Wolf3D_Head.morphTargetInfluences}
      />
      <skinnedMesh
        name="Wolf3D_Teeth"
        geometry={nodes.Wolf3D_Teeth.geometry}
        material={materials.Wolf3D_Teeth}
        skeleton={nodes.Wolf3D_Teeth.skeleton}
        morphTargetDictionary={nodes.Wolf3D_Teeth.morphTargetDictionary}
        morphTargetInfluences={nodes.Wolf3D_Teeth.morphTargetInfluences}
      />
    </group>
  );
});

useGLTF.preload("/models/karola.glb");
useGLTF.preload("/models/animation.glb");
export default Karola;