import React, { useEffect } from "react";
import { editable as e, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@theatre/r3f";
import { campFireSheet } from "../../animation/theatre";
import { Model as CampfireModel } from "./CampfireModel";
import InstancedParticles from "./InstancedParticles/InstancedParticles";
import Sparks from "./Sparks/Sparks";

export default function Campfire({ position = [4, 5.5,18.7 ] }) {
  const props = {
    range: 250,
  };

  useEffect(() => {
    campFireSheet.project.ready.then(() =>
      campFireSheet.sequence.play({
        rate: 1,
        iterationCount: Infinity,
        range: [0, 3.31],
      })
    );
  }, []);

  return (
    <>
      <SheetProvider sheet={campFireSheet}>
        {/* Groupe parent qui permet de déplacer l'ensemble */}
        <e.group theatreKey="campfireScene" position={position}>
          {/* Lumière bleue réduite ou supprimée pour éviter d'affecter les couleurs générales */}
          <e.pointLight
            theatreKey="campFireBlueLight"
            distance={10} // Réduction de la distance pour limiter son effet
            position={[-0.5, 5.7, -0.5]}
            color={0x217dc4}
            intensity={0.5} // Réduction de l'intensité
          />

          {/* Lumière orange ajustée pour mieux simuler le feu sans surcharger la scène */}
          <e.pointLight
            theatreKey="campFireLight"
            distance={15} // Réduction de la distance
            castShadow
            shadow-bias={-0.0001}
            position={[-0.5, 0.7, -0.5]}
            color={0xff7700}
            intensity={1.0} // Réduction de l'intensité pour éviter la surbrillance
          />

          <e.group theatreKey="particles" position={[-0.5, 2.5, -0.5]}>
            <InstancedParticles {...props} />
          </e.group>

          <e.group theatreKey="particlesSparks" position={[-0.5, 0.5, -0.5]}>
            <Sparks
              count={10}
              colors={["#edad2d", "#fdc555", "#e9bf6b", "#ebd4a7"]}
            />
          </e.group>

          {/* Modèle du feu de camp */}
          <CampfireModel />
        </e.group>
      </SheetProvider>
    </>
  );
}
