import React, { useState } from "react";
import TimedText from "../TimedText/TimedText";

const PhraseTab = ({ phrase }) => {
  const [activeLang1, setActiveLang1] = useState("pl");  // Set Polish as default
  const [activeLang2, setActiveLang2] = useState("en");  // Set English as the other language

  // Inverser les langues
  const toggleLanguages = () => {
    setActiveLang1((prevLang1) => {
      const newLang1 = activeLang2; // Set the active language to the opposite language
      setActiveLang2(prevLang1); // Swap the languages
      return newLang1;
    });
  };

  return (
    <div className="phrase-tab">
      {/* Add buttons or flags to toggle languages */}

      <TimedText
        src={phrase.audio}
        lang1={activeLang1}
        lang2={activeLang2}
        subtitleFile={phrase.subtitles}
        isLesson={true}
      />
    </div>
  );
};

export default PhraseTab;
