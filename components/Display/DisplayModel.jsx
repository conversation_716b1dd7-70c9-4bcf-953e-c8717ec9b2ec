import React, { useRef, useEffect } from 'react'
import { useGLTF, useAnimations } from '@react-three/drei'
import * as THREE from 'three'

export function Model(props) {
  const group = useRef()
  const environmentBoundingBox = useRef(new THREE.Box3()) // La bounding box de l'environnement
  const characterBoundingBox = useRef(new THREE.Box3()) // La bounding box du personnage

  const { nodes, materials, animations } = useGLTF('models/display.glb')
  const { actions } = useAnimations(animations, group)

  // Démarre l'animation par défaut
  useEffect(() => {
    if (actions) {
      console.log('Animations disponibles :', actions) // Vérifiez les animations disponibles dans la console
      const defaultAction = actions[Object.keys(actions)[0]] // Récupère la première animation
      if (defaultAction) defaultAction.play() // Joue l'animation si elle existe
    }
  }, [actions])


  return (
    <group {...props} dispose={null}>
    <group scale={0.01}>
      <group position={[334, 0, -181]}>
        <mesh
          castShadow
          receiveShadow
          geometry={nodes.Sweep_5_Black_Frame_0.geometry}
          material={materials.Black_Frame}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={nodes.Sweep_3_Black_Frame_0.geometry}
          material={materials.Black_Frame}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={nodes.Sweep_4_Black_Frame_0.geometry}
          material={materials.Black_Frame}
        />
      </group>
      <group position={[149.347, 0, 28.25]} rotation={[-Math.PI, 0, 0]}>
        <mesh
          castShadow
          receiveShadow
          geometry={nodes.Seat_Wood_0.geometry}
          material={materials.Wood}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={nodes.Upholstery_Fabric_0.geometry}
          material={materials.Fabric}
          position={[0, -46.353, 0]}
        />
      </group>
      <group position={[274.986, 0, -30.406]} rotation={[-Math.PI, 0, 0]}>
        <mesh
          castShadow
          receiveShadow
          geometry={nodes.Seat2_Wood_0.geometry}
          material={materials.Wood}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={nodes.Upholstery_2_Fabric_0.geometry}
          material={materials.Fabric}
          position={[0, -46.353, 0]}
        />
      </group>
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.Glass_Glass_0.geometry}
        material={materials.Glass}
        position={[69.799, 0, 0]}
        rotation={[Math.PI / 2, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.Floor_White_floor_0.geometry}
        material={materials.White_floor}
        position={[156.536, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.Art_1_Artwork_2_0.geometry}
        material={materials.Artwork_2}
        position={[350, 0, 0]}
        rotation={[Math.PI / 2, 0, Math.PI / 2]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.Art2__Artwork_1_0.geometry}
        material={materials.Artwork_1}
        position={[-70, 0, 0]}
        rotation={[Math.PI / 2, 0, 0]}
      />
    </group>
  </group>
  )
}
 

useGLTF.preload('models/display.glb')
