import React from "react";
import PropTypes from "prop-types";
import { useNavigate } from "react-router-dom";

const ErrorModal = ({ errorCode, errorMessage, onClose }) => {
  const navigate = useNavigate();

  if (!errorCode || errorCode !== 402) {
    return null; // Only render for specific error codes (e.g., 402 Payment Required)
  }

  const handlePurchaseCredits = () => {
    navigate("/shop"); // Navigate to the PurchaseCredits page
  };

  return (
    <div className="error-modal-backdrop">
      <div className="error-modal-content">
        <h2>Error {errorCode}</h2>
        <p>{errorMessage || "Payment is required to proceed."}</p>
        <div className="error-modal-actions">
          <button className="purchase-button" onClick={handlePurchaseCredits}>
            Purchase Credits
          </button>
          <button className="close-button" onClick={onClose}>
            X
          </button>
        </div>
      </div>
    </div>
  );
};

ErrorModal.propTypes = {
  errorCode: PropTypes.number.isRequired,
  errorMessage: PropTypes.string,
  onClose: PropTypes.func.isRequired,
};

export default ErrorModal;
