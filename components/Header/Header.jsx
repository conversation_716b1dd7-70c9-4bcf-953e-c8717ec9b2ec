import React, { useState, useEffect } from "react";
import {
  ProSidebar,
  Menu,
  MenuItem,
  SidebarHeader,
  <PERSON>bar<PERSON>ooter,
  SidebarContent,
} from "react-pro-sidebar";
import {

  FiLogOut,
  FiArrowLeftCircle,
  FiArrowRightCircle,
  FiSettings,
  FiShoppingCart,
  FiExternalLink,
} from "react-icons/fi";
import { FaUser, FaNewspaper, FaGraduationCap, FaVideo } from "react-icons/fa";
import { ImBubbles } from "react-icons/im";import "react-pro-sidebar/dist/css/styles.css";
import "../../styles.css";
import Select from "react-select";
import Flag from "react-world-flags";
import { useNavigate } from "react-router-dom";
import { TfiMapAlt } from "react-icons/tfi";

import { FcShop } from "react-icons/fc";

const Header = ({ initialCollapse = true, setActiveContent }) => {
  const [menuCollapse, setMenuCollapse] = useState(initialCollapse);
  const [selectedLanguage, setSelectedLanguage] = useState(null);
  const [expandedMenus, setExpandedMenus] = useState([]); // Tracks expanded submenus
  const navigate = useNavigate();

  const languageOptions = [
    { value: "pl", label: "", flag: "PL" },
    { value: "fr", label: "", flag: "FR" },
    { value: "en", label: "", flag: "GB" },
    { value: "es", label: "", flag: "ES" },
    { value: "it", label: "", flag: "IT" },
  ];

  const handleLanguageChange = (selectedOption) => {
    setSelectedLanguage(selectedOption);
  };

  useEffect(() => {
    const browserLanguage = navigator.language || navigator.userLanguage;
    const defaultLanguage =
      languageOptions.find(
        (option) => option.value === browserLanguage.substring(0, 2)
      ) || languageOptions[0];
    setSelectedLanguage(defaultLanguage);
  }, []);

  const toggleMenu = (menu) => {
    if (menuCollapse) setMenuCollapse(false); // Open sidebar on click
    setExpandedMenus((prev) =>
      prev.includes(menu) ? prev.filter((m) => m !== menu) : [...prev, menu]
    );
  };

  const handleLinkClick = (path, content) => {
    if (setActiveContent) setActiveContent(content);

    // Navigate to the path
    navigate(path);

    // Optionally, close the menu or submenu
    setMenuCollapse(true);
    setExpandedMenus([]);
  };

  return (
    <ProSidebar collapsed={menuCollapse} style={{ height: "100%", position: "fixed" }}>
      <SidebarHeader>
        <div className="logotext" style={{ textAlign: "center" }}>
          <p style={{ fontSize: "20px", fontWeight: "bold", color: "#fff" }}>
            {menuCollapse ? "" : "Menu"}
          </p>
        </div>
        <div
          className="closemenu"
          onClick={() => setMenuCollapse((prev) => !prev)}
          style={{
            cursor: "pointer",
            fontSize: "30px",
            color: "#fff",
            textAlign: "center",
          }}
        >
          {menuCollapse ? <FiArrowRightCircle /> : <FiArrowLeftCircle />}
        </div>
      </SidebarHeader>

      <SidebarContent>
        <Menu iconShape="square">
          <MenuItem
            icon={<ImBubbles />}
            onClick={() => {
              toggleMenu("home");
              handleLinkClick("/", "home");
            }}
          >
            Home
          </MenuItem>
          <MenuItem
            icon={<FaNewspaper />}
            onClick={() => {
              toggleMenu("Blog");
              handleLinkClick("/blog", "blog");
            }}
          >
            Blog
          </MenuItem>
          <MenuItem
            icon={<FaUser />}
            onClick={() => {
              toggleMenu("account");
              handleLinkClick("/account", "account");
            }}
          >
            Account
          </MenuItem>


          <MenuItem
            icon={<FcShop />}
            onClick={() => {
              toggleMenu("shop");
              handleLinkClick("/shop", "shop");
            }}
          >
            Shop
          </MenuItem>

          {/* Courses with collapsible sub-menu */}
          <MenuItem
            icon={<FaGraduationCap />}
            suffix={<span style={{ marginLeft: "auto" }}>▼</span>}
            onClick={() => toggleMenu("courses")}
          >
            Courses
          </MenuItem>
          {expandedMenus.includes("courses") && (
            <div style={{ paddingLeft: "20px" }}>
              <MenuItem onClick={() => handleLinkClick("/lessons", "beginner")}>
                Beginner
              </MenuItem>
            </div>
          )}
                    <MenuItem
            icon={<FaVideo />}
            onClick={() => {
              toggleMenu("external");
              handleLinkClick("/external", "external");
            }}
          >
            Video
          </MenuItem>
          <MenuItem
            icon={<TfiMapAlt />}
            onClick={() => {
              toggleMenu("map");
              handleLinkClick("/map", "map");
            }}
          >
            Map
          </MenuItem>
          <MenuItem
            icon={<FiSettings />}
            onClick={() => {
              toggleMenu("register-login");
              handleLinkClick("/register-login", "registerLogin");
            }}
          >
            Register/Login
          </MenuItem>
          <MenuItem
            icon={<FaUser />}
            onClick={() => {
              toggleMenu("dashboard");
              handleLinkClick("/dashboard", "dashboard");
            }}
          >
            Temp
          </MenuItem>


        </Menu>
        
      </SidebarContent>

      <SidebarFooter>
        <Menu iconShape="square">
          <MenuItem
            icon={<FiLogOut />}
            onClick={() => {
              setMenuCollapse(true);
            }}
          >
            Logout
          </MenuItem>
        </Menu>
      </SidebarFooter>
    </ProSidebar>
  );
};

export default Header;
