import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { cameraMovementSheet } from "../../../animation/theatre";

// Styles
import "../../../styles.css";
import TeamCarouselItem from "../TeamCarouselItem/TeamCaroussel";

// Team data
import teamData from "/src/components/Overlay/TeamCarousel/teamData.jsx";

const TeamCarousel = () => {
  const [active, setActive] = useState(0);

  // Theatre.js
  const obj = cameraMovementSheet.object("Team Carousel", {
    teamMemberIndex: "0",
  });

  useEffect(() => {
    return obj.onValuesChange((obj) => {
      setActive(obj.teamMemberIndex);
    });
  }, [obj]);

  // Add a check to ensure the active index is valid
  const activeMember = teamData[active] || teamData[0]; // Fallback to the first member if index is out of bounds

  return (
    <motion.div className="carousel-wrapper">
      <TeamCarouselItem key={`member-${active}`} member={activeMember} />
    </motion.div>
  );
};

export default TeamCarousel;
