import React from "react";
import { motion } from "framer-motion";
import { shallow } from "zustand/shallow";
import { useStore } from "../../../store/store";
import "../../../styles.css"; // Ensure the path to your CSS is correct

// Animation variants for the volume control slider
const controlVariants = {
  hidden: {
    y: -24,
    opacity: 0,
  },
  show: {
    y: 0,
    opacity: 1,
    transition: { duration: 0.5, delay: 0.5 },
  },
  exit: {
    y: -24,
    opacity: 0,
    transition: { duration: 0.5 },
  },
};

const SoundControl = () => {
  // Zustand store for sound level and cursor type
  const [soundLevel, updateSoundLevel, updateCursorType] = useStore(
    (store) => [
      store.soundLevel,
      store.setSoundLevel, // Ensure that setSoundLevel matches the action defined in your store
      store.updateCursorType,
    ],
    shallow
  );

  return (
    <motion.div
      variants={controlVariants}
      key="control"
      initial="hidden"
      animate="show"
      exit="exit"
      className="control"
      onMouseEnter={() => updateCursorType("hover")}
      onMouseLeave={() => updateCursorType("pointer")}
    >
      <input
        type="range"
        className="volume-slider"
        min="0"
        max="100" // Max volume level
        value={soundLevel}
        step="1"
        onChange={(e) => updateSoundLevel(parseInt(e.target.value, 10))}
        aria-label="Volume control slider"
      />
    </motion.div>
  );
};

export default SoundControl;
