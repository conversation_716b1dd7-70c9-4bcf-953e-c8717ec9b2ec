import React, { useState } from "react";
import {
  HiOutlineMusicNote,
  HiHome,
  HiOutlineUsers,
  HiOutlineBookOpen,
  HiOutlineVolumeUp,
  HiOutlineCollection,
  HiOutlineLogin,
  HiOutlineUserGroup,
  HiOutlineEye,
} from "react-icons/hi";
import changeCameraPosition from "../../../helpers/changeCameraPosition";
import { shallow } from "zustand/shallow";
import { useStore } from "../../../store/store";
import "../../../styles.css";

// Liste de navigation
const navList = [
  { title: "Home", icon: <HiHome />, position: 0 },
  { title: "Avatars", icon: <HiOutlineUsers />, position: 2 },
  { title: "Listen", icon: <HiOutlineVolumeUp />, position: 4 },
  { title: "Lessons", icon: <HiOutlineBookOpen />, position: 6 },
  { title: "Explore", icon: <HiOutlineEye />, position: 8 },
  { title: "Social", icon: <HiOutlineUserGroup />, position: 10 },
  { title: "Dashboard", icon: <HiOutlineCollection />, position: 12 },
  { title: "Account", icon: <HiOutlineLogin />, position: 14 },
  { title: "Sound", icon: <HiOutlineMusicNote />, position: 16 },

];

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [
    soundControlIsVisible,
    toggleSoundControlVisibility,
    activeNav,
    updateActiveNav,
  ] = useStore(
    (store) => [
      store.soundControlIsVisible,
      store.toggleSoundControlVisibility,
      store.activeNav,
      store.updateActiveNav,
    ],
    shallow
  );

  const handleNavBtnClick = (title, position) => {
    console.log(`Changing to section: ${title} at position: ${position}`);
    updateActiveNav(title);
    changeCameraPosition(position);
    setIsOpen(false); // Ferme le menu après le clic
  };

  return (
    <header className="navbar">
      <div className="container">
        <button className="toggle-btn" onClick={() => setIsOpen(!isOpen)}>
          {isOpen ? "x" : "="}
        </button>
        {isOpen && (
          <div className="navbar-menu">
            {navList.map((navItem, index) => (
              <div
                key={index}
                className="menu-item"
                active={activeNav === navItem.title}
                onClick={() => handleNavBtnClick(navItem.title, navItem.position)}
              >
                {navItem.icon}
                {/* Masquer le texte du titre si souhaité */}
                <span className="menu-text">{navItem.title}</span>
              </div>
            ))}
            <div
              className="menu-item"
              onClick={() => {
                toggleSoundControlVisibility(!soundControlIsVisible);
                setIsOpen(false); // Ferme le menu après avoir cliqué sur "Sound"
              }}
            >
              
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Navbar;
