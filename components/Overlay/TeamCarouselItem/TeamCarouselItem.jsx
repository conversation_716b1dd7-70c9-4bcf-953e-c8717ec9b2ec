import React from "react";

const TeamCarouselItem = ({ member }) => {
  if (!member) return null; // Safety check for undefined data

  return (
    <div className="team-carousel-item">
      <img src={member.image} alt={`${member.name}'s image`} />
      <h3>{member.name}</h3>
      <p>{member.description}</p>
      <a href={member.link.url} target="_blank" rel="noopener noreferrer">
        {member.link.title}
      </a>
    </div>
  );
};

export default TeamCarouselItem;
