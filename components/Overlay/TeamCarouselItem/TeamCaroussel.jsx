import React, { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { cameraMovementSheet } from "../../../animation/theatre";

// Styles
import "../../../styles.css";
import TeamCarouselItem from "./TeamCarouselItem.jsx";

// Team data
import teamData from "./teamData.jsx";

const TeamCarousel = () => {
  const [active, setActive] = useState(0);
  const audioRef = useRef(null); // Reference for the audio element
  const previousAudio = useRef(null); // To track the previously played audio file

  // Theatre.js configuration
  const obj = cameraMovementSheet.object("Team Carousel", {
    teamMemberIndex: "0",
  });

  useEffect(() => {
    return obj.onValuesChange((obj) => {
      setActive(obj.teamMemberIndex);
    });
  }, [obj]);

  // Play audio when active member changes, only if the member has an audio file
  useEffect(() => {
    const currentAudio = teamData[active].audio;

    if (audioRef.current && currentAudio) {
      // Only play audio if it's a different file than the previous one
      if (currentAudio !== previousAudio.current) {
        audioRef.current.src = currentAudio; // Set the audio source
        audioRef.current.currentTime = 0; // Reset audio to the beginning
        audioRef.current.play(); // Play the audio
        previousAudio.current = currentAudio; // Update the previous audio
      }
    }
  }, [active]);

  // Add a check to ensure the active index is valid
  const activeMember = teamData[active] || teamData[0]; // Fallback to the first member if index is out of bounds

  return (
    <motion.div className="carousel-wrapper">
      <TeamCarouselItem key={`member-${active}`} member={activeMember} />
      {/* Audio element */}
      <audio ref={audioRef} />
    </motion.div>
  );
};

export default TeamCarousel;
