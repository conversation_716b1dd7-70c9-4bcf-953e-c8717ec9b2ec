import React, { useState, useEffect, useCallback, useMemo } from "react";
import { motion } from "framer-motion";
import { cameraMovementSheet } from "../../../animation/theatre";
import "../../../styles.css";
import sectionsContentData from "./sectionContentData";
import { useStore } from "../../../store/store";
import { useChat } from "../../../hooks/useChat"; // Import useChat
import UserDashboard from "../../UserDashboard/UserDashboard"; // Import the User Dashboard component

const SectionDetails = () => {
  const updateCursorType = useStore((store) => store.updateCursorType);
  const [section, setSection] = useState("Home");
  const [sectionIsOpen, setSectionIsOpen] = useState(false);

  const validSections = useMemo(
    () => sectionsContentData.map((content) => content.key),
    []
  );

  const stops = useMemo(() => [6, 14], []); // Add "14" to the stops array for dashboard

  const clickHandler = useCallback(() => {
    const nextStop = stops.find(
      (stop) => stop > cameraMovementSheet.sequence.position
    );
    if (nextStop === 14) {
      if (validSections.includes("dashboard")) {
        setSection("dashboard");
        setSectionIsOpen(true);
      }
    } else {
      cameraMovementSheet.sequence.play({
        range: [cameraMovementSheet.sequence.position, nextStop],
        rate: 0.3,
      });
    }
  }, [stops, validSections]);

  const obj = cameraMovementSheet.object("Section Overlay", {
    visible: false,
    section: "Home",
  });

  useEffect(() => {
    const handleValuesChange = (updatedObj) => {
      let newSection = updatedObj.section || "Home";

      // Validate section
      if (!validSections.includes(newSection)) {
        console.warn(
          `Section "${newSection}" not found in sectionsContentData. Reassigning to "Home".`
        );
        newSection = "Home";
      }

      setSection(newSection);
      setSectionIsOpen(updatedObj.visible);
    };

    const cleanup = obj.onValuesChange(handleValuesChange);
    return () => cleanup();
  }, [obj, validSections]);

  // Import messages from Chat context
  const { messages } = useChat();

  // Filter messages from the avatar
  const avatarMessages = useMemo(
    () => messages.filter((msg) => msg.role === "assistant"),
    [messages]
  );

  // Get current content
  const currentContent = useMemo(() => {
    const content = sectionsContentData.find(
      (content) => content.key === section
    );
    console.log("Current section key:", section);
    console.log("Current content:", content);
    return content;
  }, [section]);

  if (!currentContent) {
    console.warn(`Section "${section}" not found in sectionsContentData`);
    return <div>Section not found.</div>;
  }

  return (
    <div className="section-container">
      {sectionIsOpen && (
        <motion.div
          key={currentContent.key}
          className="section-overlay"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 50 }}
          transition={{ duration: 0.3 }}
        >
          <button
            className="close-button"
            onClick={() => setSectionIsOpen(false)}
            aria-label="Close"
          >
            ×
          </button>
          <div className="section-text">
            <h1>{currentContent.title}</h1>
            {section === "dashboard" ? (
              <UserDashboard /> // Render User Dashboard component for the dashboard section
            ) : (
              currentContent.content
            )}

            {/* Display avatar messages */}
            {avatarMessages.length > 0 && (
              <div className="avatar-messages">
                <h2>Messages de l'avatar :</h2>
                {avatarMessages.map((msg, index) => (
                  <p key={index}>{msg.content}</p>
                ))}
              </div>
            )}
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default SectionDetails;
