// Imports
import React, { useRef, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import TimedText from "../../TimedText/TimedText.jsx"; // Composant TimedText pour les sous-titres synchronisés
import UserDashboard from "../../UserDashboard/UserDashboard.jsx"; // Tableau de bord utilisateur
import Lessons from "../../Lessons/Lessons.jsx"; // Section leçons
import SocialSection from "../../SocialSection/SocialSection.jsx"; // Section sociale
import SoundControl from "../../SoundControl/SoundControl.jsx"; // Contrôle du son

// URL de base pour les requêtes API
const API_URL = import.meta.env.VITE_API_URL || 'https://serveur-avatar.hexadecaedre.com';

// Composant AudioPlayer pour la lecture de fichiers audio avec chargement différé
const AudioPlayer = ({ src }) => {
  const audioRef = useRef(null);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.play().catch((error) => console.error("Playback error:", error));
    }
  }, [src]);

  return <audio ref={audioRef} src={src} controls preload="metadata" />;
};

AudioPlayer.propTypes = {
  src: PropTypes.string.isRequired,
};

// Composant de champ de formulaire réutilisable
const FormInput = ({ label, type, id, name, placeholder, value, onChange }) => (
  <div className="form-group">
    <label htmlFor={id}>{label}</label>
    <input 
      type={type} 
      id={id} 
      name={name} 
      placeholder={placeholder} 
      value={value} 
      onChange={onChange} 
      required 
    />
  </div>
);

FormInput.propTypes = {
  label: PropTypes.string.isRequired,
  type: PropTypes.string.isRequired,
  id: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  placeholder: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
};

// Composant RegisterForm pour l'inscription
const RegisterForm = () => {
  const [registerData, setRegisterData] = useState({ username: '', email: '', password: '' });
  const [errors, setErrors] = useState({});
  const [message, setMessage] = useState('');

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setRegisterData({ ...registerData, [name]: value });
  };

  const validateForm = () => {
    let formErrors = {};
    if (!registerData.username.trim()) formErrors.username = 'Username is required';
    if (!registerData.email.includes('@')) formErrors.email = 'Email is invalid';
    if (registerData.password.length < 6) formErrors.password = 'Password must be at least 6 characters';
    return formErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const formErrors = validateForm();
    if (Object.keys(formErrors).length === 0) {
      try {
        const response = await fetch(`${API_URL}/auth/register`, { // Inclure /auth
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(registerData),
        });
        if (response.ok) {
          setMessage('Registration successful');
          setRegisterData({ username: '', email: '', password: '' });
        } else {
          const data = await response.json();
          setMessage(data.message || 'Failed to register');
        }
      } catch (error) {
        setMessage('Error: ' + error.message);
      }
    } else {
      setErrors(formErrors);
    }
  };
  

  return (
    <div className="form-container centered">
      <h2>Register</h2>
      <form className="register-form" onSubmit={handleSubmit}>
        <FormInput
          label="Username:"
          type="text"
          id="username"
          name="username"
          placeholder="Enter your username"
          value={registerData.username}
          onChange={handleInputChange}
        />
        {errors.username && <p className="error-text">{errors.username}</p>}
        
        <FormInput
          label="Email:"
          type="email"
          id="email"
          name="email"
          placeholder="Enter your email"
          value={registerData.email}
          onChange={handleInputChange}
        />
        {errors.email && <p className="error-text">{errors.email}</p>}
        
        <FormInput
          label="Password:"
          type="password"
          id="password"
          name="password"
          placeholder="Enter your password"
          value={registerData.password}
          onChange={handleInputChange}
        />
        {errors.password && <p className="error-text">{errors.password}</p>}
        
        <button type="submit" className="register-btn">Register</button>
        {message && <p>{message}</p>}
      </form>
    </div>
  );
};

// Composant LoginForm pour la connexion
const LoginForm = () => {
  const [loginData, setLoginData] = useState({ email: '', password: '' });
  const [errors, setErrors] = useState({});
  const [message, setMessage] = useState('');

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setLoginData({ ...loginData, [name]: value });
  };

  const validateForm = () => {
    let formErrors = {};
    if (!loginData.email.includes('@')) formErrors.email = 'Email is invalid';
    if (loginData.password.length < 6) formErrors.password = 'Password must be at least 6 characters';
    return formErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const formErrors = validateForm();
    if (Object.keys(formErrors).length === 0) {
      try {
        const response = await fetch(`${API_URL}/auth/login`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(loginData),
        });
        const data = await response.json();
        if (response.ok) {
          localStorage.setItem('token', data.token);
          setMessage('Login successful');
          setLoginData({ email: '', password: '' });
        } else {
          setMessage(data.message || 'Failed to login');
        }
      } catch (error) {
        setMessage('Error: ' + error.message);
      }
    } else {
      setErrors(formErrors);
    }
  };

  return (
    <div className="form-container centered">
      <h2>Login</h2>
      <form className="login-form" onSubmit={handleSubmit}>
        <FormInput
          label="Email:"
          type="email"
          id="login-email"
          name="email"
          placeholder="Enter your email"
          value={loginData.email}
          onChange={handleInputChange}
        />
        {errors.email && <p className="error-text">{errors.email}</p>}
        
        <FormInput
          label="Password:"
          type="password"
          id="login-password"
          name="password"
          placeholder="Enter your password"
          value={loginData.password}
          onChange={handleInputChange}
        />
        {errors.password && <p className="error-text">{errors.password}</p>}
        
        <button type="submit" className="login-btn">Login</button>
        {message && <p>{message}</p>}
      </form>
    </div>
  );
};

// Tabs pour Register et Login
const RegisterLoginTabs = () => {
  const [activeTab, setActiveTab] = useState('login');

  return (
    <div className="tabs-container">
      <div className="tabs-header">
      <button
          className={`tab-button ${activeTab === 'login' ? 'active' : ''}`}
          onClick={() => setActiveTab('login')}
        >
          Login
        </button>
        <button
          className={`tab-button ${activeTab === 'register' ? 'active' : ''}`}
          onClick={() => setActiveTab('register')}
        >
          Register
        </button>

      </div>
      <div className="tabs-content">
        {activeTab === 'login' && <LoginForm />}
        {activeTab === 'register' && <RegisterForm />}
      </div>
    </div>
  );
};

// Données des sections avec le contenu associé pour chaque onglet
const sectionsContentData = [
  {
    key: "Home",
    title: "",
    content: (
      <TimedText
        src="/audio/polish/feelpolska-intro.mp3"
        lang1="pl"
        lang2="en"
        subtitleFile="/subtitles/subtitles-home.json"
       
        onMouthCueUpdate={(activeCues) => {
          console.log("Active mouth cues:", activeCues);
        }}
      />
    ),
  },
  {
    key: "Sound",
    title: "",
    content: (
      <>
        <SoundControl />
      </>
    ),
  },
  {
    key: "register-login",
    title: "",
    content: <RegisterLoginTabs />,
  },
  {
    key: "Social",
    title: "",
    content: <SocialSection />,
  },
  {
    key: "Lessons",
    title: "",
    content: <Lessons />,
  },
  {
    key: "dashboard",
    title: "",
    content: <UserDashboard />,
  },
];

export default sectionsContentData;
