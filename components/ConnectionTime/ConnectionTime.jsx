// ConnectionTime.jsx
import React, { useEffect, useState } from 'react';

const formatTime = (seconds) => {
  const hrs = Math.floor(seconds / 3600);
  const mins = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  return `${hrs > 0 ? hrs + 'h ' : ''}${mins > 0 ? mins + 'm ' : ''}${secs}s`;
};

const ConnectionTime = () => {
  const [connectionTime, setConnectionTime] = useState(0); // Temps en secondes

  useEffect(() => {
    const startTime = Date.now();

    // Met à jour le temps chaque seconde
    const intervalId = setInterval(() => {
      const elapsedSeconds = Math.floor((Date.now() - startTime) / 1000);
      setConnectionTime(elapsedSeconds);
    }, 1000);

    // Nettoyage à la déconnexion pour éviter les fuites de mémoire
    return () => clearInterval(intervalId);
  }, []);

  return (
    <div className="connection-time">
      <h3>Connection Time</h3>
      <p>{formatTime(connectionTime)}</p>
    </div>
  );
};

export default ConnectionTime;
