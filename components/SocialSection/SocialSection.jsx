// SocialSection.jsx
import React, { useState } from 'react';
import FriendsList from './FriendsList';
import CreatePost from './CreatePost';
import PostFeed from './PostFeed';

const SocialSection = () => {
  const [friends, setFriends] = useState([]);
  const [posts, setPosts] = useState([]);

  const addFriend = (friendName) => {
    setFriends([...friends, { id: Date.now(), name: friendName }]);
  };

  const removeFriend = (friendId) => {
    setFriends(friends.filter(friend => friend.id !== friendId));
  };

  const addNewPost = (newPost) => {
    setPosts([newPost, ...posts]);
  };

  return (
    <div className="social-section">
      <h2>Social Network</h2>
      <FriendsList friends={friends} addFriend={addFriend} removeFriend={removeFriend} />
      <CreatePost addNewPost={addNewPost} />
      <PostFeed posts={posts} friends={friends} />
    </div>
  );
};

export default SocialSection;
