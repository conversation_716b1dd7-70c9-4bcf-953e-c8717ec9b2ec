// CreatePost.jsx
import React, { useState } from 'react';

const CreatePost = ({ addNewPost }) => {
  const [content, setContent] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!content) return;

    const newPost = {
      id: Date.now(),
      author: "You", // L'auteur peut être l'utilisateur actuel
      content,
    };

    addNewPost(newPost);
    setContent('');
  };

  return (
    <div className="create-post">
      <form onSubmit={handleSubmit}>
        <textarea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          placeholder="What's on your mind?"
        />
        <button type="submit">Post</button>
      </form>
    </div>
  );
};

export default CreatePost;
