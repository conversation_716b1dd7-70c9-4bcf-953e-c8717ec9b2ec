// PostFeed.jsx
import React from 'react';

const PostFeed = ({ posts, friends }) => {
  // Génère un tableau de noms d'amis pour une recherche rapide
  const friendNames = friends.map(friend => friend.name);

  return (
    <div className="post-feed">
      {posts.length === 0 ? (
        <p>No posts yet</p>
      ) : (
        posts.map(post => (
          <div key={post.id} className="post">
            <p><strong>{post.author}</strong>: {post.content}</p>
          </div>
        ))
      )}
    </div>
  );
};

export default PostFeed;
