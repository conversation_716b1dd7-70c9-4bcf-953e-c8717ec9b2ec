// FriendsList.jsx
import React, { useState } from 'react';

const FriendsList = ({ friends, addFriend, removeFriend }) => {
  const [friendName, setFriendName] = useState('');

  const handleAddFriend = () => {
    if (friendName) {
      addFriend(friendName);
      setFriendName('');
    }
  };

  return (
    <div className="friends-list">
      <h3>Your Friends</h3>
      <ul>
        {friends.map(friend => (
          <li key={friend.id}>
            {friend.name}
            <button onClick={() => removeFriend(friend.id)}>Remove</button>
          </li>
        ))}
      </ul>
      <input
        type="text"
        value={friendName}
        onChange={(e) => setFriendName(e.target.value)}
        placeholder="Add a friend"
      />
      <button onClick={handleAddFriend}>Add Friend</button>
    </div>
  );
};

export default FriendsList;
