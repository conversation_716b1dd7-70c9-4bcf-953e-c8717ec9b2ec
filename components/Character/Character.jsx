import { use<PERSON>rame, useThree } from '@react-three/fiber';
import React, {
  useCallback,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
  Suspense,
} from 'react';
import { useFBX } from '@react-three/drei';
import * as THREE from 'three';

// Importing audio files
/* import backgroundMusicFile from '/audio/gallery.mp3';
 *//* import backgroundMusicFile2 from '/audio/chopin.mp3';
 */
import clickSoundFile from '/audio/hey.mp3';

const Character = forwardRef(({ obstacles, joystickDirection }, ref) => {
  const characterGroup = useRef();
  const velocity = useRef(new THREE.Vector3(0, 0, 0));
  const direction = new THREE.Vector3();
  const isMoving = useRef(false);

  // Audio references
/*   const backgroundMusicRef = useRef(new Audio(backgroundMusicFile));
 *//*   const backgroundMusicRef2 = useRef(new Audio(backgroundMusicFile2));
 */
  const clickSoundRef = useRef(new Audio(clickSoundFile));

  const activeAnimation = useRef({
    forward: false,
    backward: false,
    left: false,
    right: false,
    run: false,
    dance: false,
    speak: false, // Added for the 'speak' animation
  });

  const decceleration = new THREE.Vector3(-0.005, -0.001, -5.0);
  const acceleration = new THREE.Vector3(500, 0, 50000);
  const maxSpeed = 22;

  const model = useFBX('./character/seba.fbx');
  model.scale.setScalar(0.05);
  model.traverse((f) => {
    if (f.isMesh) {
      f.castShadow = true;
      f.receiveShadow = true;
      if (f.material) {
        f.material.shading = THREE.FlatShading;
        f.material.roughness = 1;
        f.material.metalness = 0;
      }
    }
  });

  const mixer = useRef(new THREE.AnimationMixer(model));
  const animations = useRef({});

  const loadAndSetupAnimation = (path, name) => {
    const animation = useFBX(path).animations[0];
    animation.tracks = animation.tracks.filter((track) => {
      const targetNode = model.getObjectByName(track.name.split('.')[0]);
      return !!targetNode;
    });
    animations.current[name] = mixer.current.clipAction(animation);
  };

  // Load standard animations
  loadAndSetupAnimation('./character/Idle.fbx', 'idle');
  loadAndSetupAnimation('./character/Walking.fbx', 'walk');
  
  const currAction = useRef(animations.current['idle']);
  const prevAction = useRef(currAction.current);
  currAction.current.play();

  

  useEffect(() => {
    if (characterGroup.current) {
      characterGroup.current.position.set(45, -36, 10);
    }
  }, []);

  const { camera } = useThree();
  const raycaster = useRef(new THREE.Raycaster());
  const mouse = useRef(new THREE.Vector2());

  const handleMouseClick = useCallback(
    (event) => {
      mouse.current.x = (event.clientX / window.innerWidth) * 2 - 1;
      mouse.current.y = -(event.clientY / window.innerHeight) * 2 + 1;
      raycaster.current.setFromCamera(mouse.current, camera);
      const intersects = raycaster.current.intersectObject(characterGroup.current, true);
      if (intersects.length > 0) {
        console.log('Character clicked!');

        // Play click sound
        clickSoundRef.current.currentTime = 0; // Reset to start
        clickSoundRef.current.play().catch((error) => {
          console.error('Click sound play error:', error);
        });

        // Trigger 'speak' animation
        activeAnimation.current.speak = true;
      }
    },
    [camera]
  );

  useEffect(() => {
    // Add the click event listener
    window.addEventListener('click', handleMouseClick);

    // Cleanup on unmount
    return () => {
      window.removeEventListener('click', handleMouseClick);
    };
  }, [handleMouseClick]);
/* 
  const handleKey = useCallback((event, value) => {
    switch (event.keyCode) {
      case 87:
        activeAnimation.current.forward = value;
        break; // W
      case 65:
        activeAnimation.current.left = value;
        break; // A
      case 83:
        activeAnimation.current.backward = value;
        break; // S
      case 68:
        activeAnimation.current.right = value;
        break; // D
      case 16:
        activeAnimation.current.run = value;
        break; // Shift
      default:
        break;
    }
  }, []); */

/*   useEffect(() => {
    const handleKeyDown = (event) => handleKey(event, true);
    const handleKeyUp = (event) => handleKey(event, false);

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [handleKey]); */

  useEffect(() => {
    // Joystick control updates
    activeAnimation.current.forward = joystickDirection === 'FORWARD';
    activeAnimation.current.backward = joystickDirection === 'BACKWARD';
    activeAnimation.current.left = joystickDirection === 'LEFT';
    activeAnimation.current.right = joystickDirection === 'RIGHT';
  }, [joystickDirection]);

  const characterState = (delta) => {
    const frameDecceleration = decceleration
      .clone()
      .multiply(velocity.current)
      .multiplyScalar(delta);
    velocity.current.add(frameDecceleration);

    const acc = acceleration.clone();

    if (activeAnimation.current.run) acc.multiplyScalar(2.0);
    if (currAction.current === animations.current['dance']) acc.setScalar(0);

    if (activeAnimation.current.left) characterGroup.current.rotation.y += 2.0 * delta;
    if (activeAnimation.current.right) characterGroup.current.rotation.y -= 2.0 * delta;

    if (activeAnimation.current.forward) velocity.current.z += acc.z * delta;
    if (activeAnimation.current.backward) velocity.current.z -= acc.z * delta;

    velocity.current.clampLength(0, maxSpeed);

    direction.set(0, 0, 1).applyQuaternion(characterGroup.current.quaternion);
    const nextPosition = direction.clone().multiplyScalar(velocity.current.z * delta);

    // Update position
    characterGroup.current.position.add(nextPosition);

    isMoving.current = velocity.current.length() > 0.1;
  };

  useFrame((_, delta) => {
    prevAction.current = currAction.current;
  
    if (activeAnimation.current.speak && !mixer.current.existingAction(currAction.current.getClip())) {
      // Sélectionner une animation 'speak' au hasard
      const speakAnimationKeys = ['speak1', 'speak2', 'speak3'];
      const randomKey =
        speakAnimationKeys[Math.floor(Math.random() * speakAnimationKeys.length)];
      currAction.current = animations.current[randomKey];
  
      if (currAction.current) {
        // Jouer l'animation 'speak'
        currAction.current.setLoop(THREE.LoopOnce);
        currAction.current.clampWhenFinished = true;
        currAction.current.reset().fadeIn(0.2).play();
  
        // Réinitialiser le drapeau 'speak'
        activeAnimation.current.speak = false;
  
        // Transition après la fin de l'animation
        const onFinish = () => {
          currAction.current.fadeOut(0.2);
          currAction.current = animations.current['idle'];
          if (currAction.current) {
            currAction.current.reset().fadeIn(0.2).play();
          }
          mixer.current.removeEventListener('finished', onFinish);
        };
        mixer.current.addEventListener('finished', onFinish);
      }
    } else {
      // Gestion des animations de mouvement
      currAction.current =
        activeAnimation.current.forward ||
        activeAnimation.current.backward ||
        activeAnimation.current.left ||
        activeAnimation.current.right
          ? activeAnimation.current.run
            ? animations.current['run']
            : animations.current['walk']
          : animations.current['idle'];
  
      if (prevAction.current !== currAction.current) {
        prevAction.current.fadeOut(0.2);
        currAction.current.reset().fadeIn(0.2).play();
      }
    }
  
    characterState(delta);
    mixer.current.update(delta);
  });
  

  useImperativeHandle(ref, () => ({
    get position() {
      return characterGroup.current.position;
    },
    get quaternion() {
      return characterGroup.current.quaternion;
    },
    get isMoving() {
      return isMoving.current;
    },
  }));

  return (
    <group ref={characterGroup}>
      <Suspense fallback={null}>
        <primitive object={model} />
      </Suspense>
    </group>
  );
});

export default Character;