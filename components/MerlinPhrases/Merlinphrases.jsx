const MerlinPhrases = [
    {
      en: "Did someone say magic? Oh wait, that’s just my breakfast talking.",
      pl: "<PERSON><PERSON><PERSON> mówił o magii? A nie, to tylko moje ś<PERSON>danie mówi.",
      audio: "/audio/magic_breakfast.mp3",
      subtitles: "/subtitles/magic_breakfast.json",
    },
    {
      en: "I’m not old, I’m just... well-aged, like fine cheese.",
      pl: "Nie jestem stary, jestem dobrze do<PERSON>, jak dobry ser.",
      audio: "/audio/well_aged.mp3",
      subtitles: "/subtitles/well_aged.json",
    },
    {
      en: "Do you think dragons snore? Asking for a friend.",
      pl: "<PERSON><PERSON><PERSON><PERSON>, że smoki chrapią? Pytam dla kolegi.",
      audio: "/audio/dragons_snore.mp3",
      subtitles: "/subtitles/dragons_snore.json",
    },
    {
      en: "If I had a gold coin for every spell I messed up, I’d be rich!",
      pl: "Gdybym miał złotą monetę za każdy źle rzucony czar, byłbym bogaty!",
      audio: "/audio/gold_coin.mp3",
      subtitles: "/subtitles/gold_coin.json",
    },
    {
      en: "Never trust a wizard with burnt eyebrows.",
      pl: "Nigdy nie ufaj czarodziejowi z przypalonymi brwiami.",
      audio: "/audio/burnt_eyebrows.mp3",
      subtitles: "/subtitles/burnt_eyebrows.json",
    },
  ];
  
  export default MerlinPhrases;
  