import React, { useState } from "react";
import axios from "axios";
import { loadStripe } from "@stripe/stripe-js";
import "../../styles.css"; // Import external CSS file

const stripePromise = loadStripe("pk_test_51NeHO4EXjbOO4DjBiGQA0WeavkIg3aor65b40PqM4zAe7l1JKcy99jUOKAuP3sztrMNihZV0EAFGWMdjEmpYatiJ00cj4OXkcI"); // Remplacez par votre clé publique Stripe

const PurchaseCredits = () => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [selectedPack, setSelectedPack] = useState(null);

    const creditPacks = [
        { id: 1, credits: 100, price: 5, description: "Parfait pour les débutants." },
        { id: 2, credits: 500, price: 20, description: "Idéal pour les apprenants réguliers." },
        { id: 3, credits: 1000, price: 30, description: "Meilleur rapport qualité-prix pour un apprentissage intensif." },
    ];

    const handlePurchase = async () => {
        if (!selectedPack) {
            setError("Veuillez sélectionner un pack de crédits.");
            return;
        }

        setLoading(true);
        setError(null);

        try {
            const token = localStorage.getItem("token"); // Remplacez par votre logique d'authentification
            const response = await axios.post(
                "https://serveur-avatar.hexadecaedre.com/api/payment/start",
                { credits: selectedPack.credits },
                {
                    headers: { Authorization: `Bearer ${token}` },
                }
            );

            const stripe = await stripePromise;
            await stripe.redirectToCheckout({ sessionId: response.data.url });
        } catch (err) {
            console.error("Erreur lors de l'initialisation du paiement:", err.message);
            setError("Le paiement a échoué. Veuillez réessayer.");
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="purchase-credits-container">
            <div className="purchase-credits-content">
                <h1>Achetez des Crédits</h1>
                <p>
                    Bienvenue dans notre application d'apprentissage des langues ! Achetez des crédits pour accéder à des leçons interactives, des outils
                    de synthèse vocale (TTS), de reconnaissance vocale (STT) et des avatars pilotés par l'IA pour une expérience immersive.
                </p>

                {error && <p className="error">{error}</p>}

                <div className="credit-packs">
                    {creditPacks.map((pack) => (
                        <div
                            key={pack.id}
                            className={`credit-pack ${selectedPack?.id === pack.id ? "selected" : ""}`}
                            onClick={() => setSelectedPack(pack)}
                        >
                            <h2>{pack.credits} Crédits</h2>
                            <p>{pack.price.toFixed(2)} €</p>
                            <p className="description">{pack.description}</p>
                        </div>
                    ))}
                </div>

                {selectedPack && (
                    <div className="selected-pack-summary">
                        <h3>Pack Sélectionné :</h3>
                        <p>
                            {selectedPack.credits} Crédits pour {selectedPack.price.toFixed(2)} €
                        </p>
                    </div>
                )}

                <button onClick={handlePurchase} disabled={loading || !selectedPack}>
                    {loading ? "Traitement en cours..." : "Acheter des Crédits"}
                </button>
            </div>
        </div>
    );
};

export default PurchaseCredits;