import React, { useRef, useEffect } from 'react'
import { useGLTF, useAnimations } from '@react-three/drei'
import * as THREE from 'three'

export function Model(props) {
  const group = useRef()
  const environmentBoundingBox = useRef(new THREE.Box3()) // La bounding box de l'environnement
  const characterBoundingBox = useRef(new THREE.Box3()) // La bounding box du personnage

  const { nodes, materials, animations } = useGLTF('models/2-mens-speaking.glb')
  const { actions } = useAnimations(animations, group)

  // Démarre l'animation par défaut
  useEffect(() => {
    if (actions) {
      console.log('Animations disponibles :', actions) // Vérifiez les animations disponibles dans la console
      const defaultAction = actions[Object.keys(actions)[0]] // Récupère la première animation
      if (defaultAction) defaultAction.play() // Joue l'animation si elle existe
    }
  }, [actions])


  return (
    <group ref={group} {...props} dispose={null}>
      <group name="Scene">
        <group name="Sketchfab_model" rotation={[-Math.PI / 2, 0, 0]}>
          <group name="root">
            <group name="GLTF_SceneRootNode" rotation={[Math.PI / 2, 0, 0]}>
              <group
                name="Object_660_808"
                position={[0.864, 0.891, -0.285]}
                rotation={[-Math.PI / 2, 0, 0]}
                scale={0.025}>
                <group name="GLTF_created_26">
                  <skinnedMesh
                    name="Object_908"
                    geometry={nodes.Object_908.geometry}
                    material={materials.mtl_Male013Skin_001_Color_19}
                    skeleton={nodes.Object_908.skeleton}
                  />
                  <primitive object={nodes.GLTF_created_26_rootJoint} />
                </group>
              </group>
              <group
                name="Object_916_839"
                position={[-0.511, 0.899, -0.167]}
                rotation={[-Math.PI / 2, 0, 0]}
                scale={0.025}>
                <group name="GLTF_created_27">
                  <skinnedMesh
                    name="Object_944"
                    geometry={nodes.Object_944.geometry}
                    material={materials.mtl_Male013Skin_001_Color_26}
                    skeleton={nodes.Object_944.skeleton}
                  />
                  <primitive object={nodes.GLTF_created_27_rootJoint} />
                </group>
              </group>
            </group>
          </group>
        </group>
      </group>
    </group>
  )
}
 

useGLTF.preload('models/2-mens-speaking.glb')
