// LoginDialog.js
import React, { useEffect, useRef, useCallback, useState } from "react";
import PropTypes from "prop-types";
import { useNavigate } from "react-router-dom";
import "../../styles.css";

// Base API URL for WordPress
const API_URL = 'https://feelpolska.com/wp-json';

// Reusable Input Component
const FormInput = React.memo(function FormInput({
  label,
  type,
  id,
  name,
  placeholder,
  value,
  onChange,
  error,
}) {
  return (
    <div className="form-group">
      <label htmlFor={id}>{label}</label>
      <input
        type={type}
        id={id}
        name={name}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        required
        className={error ? 'input-error' : ''}
      />
      {error && <p className="error-text">{error}</p>}
    </div>
  );
});

FormInput.propTypes = {
  label: PropTypes.string.isRequired,
  type: PropTypes.string.isRequired,
  id: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  placeholder: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  error: PropTypes.string,
};

const validateLoginData = (data) => {
  const errors = {};
  if (!data.username.trim()) errors.username = 'Username is required.';
  if (data.password.length < 6) errors.password = 'Password must be at least 6 characters.';
  return errors;
};

// LoginForm Component
const LoginForm = React.memo(function LoginForm({ onSuccess, onClose }) {
  const navigate = useNavigate();
  const [loginData, setLoginData] = useState({ username: '', password: '' });
  const [errors, setErrors] = useState({});
  const [message, setMessage] = useState('');

  const handleInputChange = useCallback((e) => {
    const { name, value } = e.target;
    setLoginData((prev) => ({ ...prev, [name]: value }));
  }, []);

  // Dans la fonction handleSubmit du composant LoginForm
const handleSubmit = useCallback(
  async (e) => {
    e.preventDefault();
    const formErrors = validateLoginData(loginData);
    if (Object.keys(formErrors).length === 0) {
      try {
        const response = await fetch(`${API_URL}/jwt-auth/v1/token`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(loginData),
        });
        const data = await response.json();
        
        if (response.ok) {
          localStorage.setItem('token', data.token);
          setMessage('Login successful!');
          
          // Fermer la modale ET recharger la page
          if (onClose) {
            onClose(); // Ferme la modale
            window.location.reload(); // Recharge la page
          }
        } else {
          setMessage(data.message || 'Login failed.');
        }
      } catch (error) {
        setMessage('An error occurred: ' + error.message);
      }
    } else {
      setErrors(formErrors);
    }
  },
  [loginData, onClose] // Dépendances mises à jour
);

  return (
    <form className="form-container" onSubmit={handleSubmit}>
      <FormInput
        label="Username:"
        type="text"
        id="username"
        name="username"
        placeholder="Enter your username"
        value={loginData.username}
        onChange={handleInputChange}
        error={errors.username}
      />
      <FormInput
        label="Password:"
        type="password"
        id="password"
        name="password"
        placeholder="Enter your password"
        value={loginData.password}
        onChange={handleInputChange}
        error={errors.password}
      />
      <button type="submit" className="login-btn">Login</button>
      {message && <p className="form-message">{message}</p>}
    </form>
  );
});

LoginForm.propTypes = {
  onSuccess: PropTypes.func,
  onClose: PropTypes.func,
};

const LoginDialog = ({ onClose }) => {
  const dialogRef = useRef(null);

  const handleBackdropClick = useCallback(
    (e) => {
      if (e.target.classList.contains("login-dialog-backdrop") && onClose) {
        onClose();
      }
    },
    [onClose]
  );

  const handleKeyDown = useCallback(
    (e) => {
      if (e.key === "Escape" && onClose) {
        onClose();
      }
    },
    [onClose]
  );

  useEffect(() => {
    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [handleKeyDown]);

  return (
    <div
      className="login-dialog-backdrop"
      onClick={handleBackdropClick}
      role="dialog"
      aria-labelledby="login-dialog-title"
    >
      <div className="login-dialog-content" ref={dialogRef} tabIndex={-1}>
        <h2 id="login-dialog-title" className="login-dialog-title">
          Login Required
        </h2>
        <div class="styled-link-container">
    <a href="https://feelpolska.com/register/" class="styled-link" target="_blank">Registration page</a>
</div>
        <LoginForm 
          onSuccess={onClose} 
          onClose={onClose} 
        />
        <button
          onClick={onClose}
          className="login-dialog-button"
        >
          Close
        </button>
      </div>
    </div>
  );
};

LoginDialog.propTypes = {
  onClose: PropTypes.func.isRequired,
};

export default LoginDialog;