const Character = forwardRef(({ environment, joystickDirection }, ref) => {
  const characterGroup = useRef();
  const velocity = useRef(new THREE.Vector3(0, 0, 0));
  const direction = new THREE.Vector3();
  const isMoving = useRef(false); // Track if character is moving
  const boundingBox = useRef(new THREE.Box3()); // Character bounding box

  const activeAnimation = useRef({
    forward: false,
    backward: false,
    left: false,
    right: false,
    run: false,
    dance: false,
  });

  const decceleration = new THREE.Vector3(-0.005, -0.001, -5.0);
  const acceleration = new THREE.Vector3(5, 0, 500);
  const maxSpeed = 15;

  const model = useFBX("./character/merlin.fbx");
  model.scale.setScalar(1);
  model.traverse((f) => {
    if (f.material) {
      f.material.shading = THREE.FlatShading; // Apply flat shading
      f.material.roughness = 1; // High roughness for a matte finish
      f.material.metalness = 0; // No metalness (non-shiny)
    }
  });

  const mixer = useRef(new THREE.AnimationMixer(model));
  const animations = useRef({});

  const loadAndSetupAnimation = (path, name) => {
    const animation = useFBX(path).animations[0];
    animation.tracks = animation.tracks.filter((track) => {
      const targetNode = model.getObjectByName(track.name.split(".")[0]);
      return !!targetNode;
    });
    animations.current[name] = mixer.current.clipAction(animation);
  };

  loadAndSetupAnimation("./character/Idle.fbx", "idle");
  loadAndSetupAnimation("./character/walking.fbx", "walk");
  loadAndSetupAnimation("./character/running.fbx", "run");
  loadAndSetupAnimation("./character/dance.fbx", "dance");

  const currAction = useRef(animations.current["idle"]);
  const prevAction = useRef(currAction.current);
  currAction.current.play();

  useEffect(() => {
    if (characterGroup.current) {
      characterGroup.current.position.set(75, 0, -50);
    }
  }, []);

  const handleKey = useCallback((event, value) => {
    switch (event.keyCode) {
      case 87:
        activeAnimation.current.forward = value;
        break; // W
      case 65:
        activeAnimation.current.left = value;
        break; // A
      case 83:
        activeAnimation.current.backward = value;
        break; // S
      case 68:
        activeAnimation.current.right = value;
        break; // D
      case 16:
        activeAnimation.current.run = value;
        break; // Shift
      default:
        break;
    }
  }, []);

  useEffect(() => {
    const handleKeyDown = (event) => handleKey(event, true);
    const handleKeyUp = (event) => handleKey(event, false);
    document.addEventListener("keydown", handleKeyDown);
    document.addEventListener("keyup", handleKeyUp);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.removeEventListener("keyup", handleKeyUp);
    };
  }, [handleKey]);

  const checkCollisions = (nextPosition) => {
    if (environment?.boundingBox) {
      // Simulate the next position of the character
      const characterBoundingBox = boundingBox.current.clone();
      characterBoundingBox.translate(nextPosition);

      // If the next position intersects with the environment's bounding box, block the movement
      if (characterBoundingBox.intersectsBox(environment.boundingBox)) {
        return true; // Collision detected
      }
    }
    return false; // No collision
  };

  const characterState = (delta) => {
    const frameDecceleration = decceleration
      .clone()
      .multiply(velocity.current)
      .multiplyScalar(delta);
    velocity.current.add(frameDecceleration);

    const acc = acceleration.clone();
    if (activeAnimation.current.run) acc.multiplyScalar(2.0);
    if (currAction.current === animations.current["dance"]) acc.setScalar(0);

    if (activeAnimation.current.left)
      characterGroup.current.rotation.y += 2.0 * delta;
    if (activeAnimation.current.right)
      characterGroup.current.rotation.y -= 2.0 * delta;

    if (activeAnimation.current.forward) velocity.current.z += acc.z * delta;
    if (activeAnimation.current.backward) velocity.current.z -= acc.z * delta;

    velocity.current.clampLength(0, maxSpeed);

    direction.set(0, 0, 1).applyQuaternion(characterGroup.current.quaternion);
    const nextPosition = direction
      .clone()
      .multiplyScalar(velocity.current.z * delta);

    // Check collisions before applying movement
    if (!checkCollisions(nextPosition)) {
      characterGroup.current.position.add(nextPosition);
    } else {
      velocity.current.set(0, 0, 0); // Stop the character on collision
    }

    // Update movement state
    isMoving.current = velocity.current.length() > 0.1;
  };

  useFrame((_, delta) => {
    prevAction.current = currAction.current;

    currAction.current =
      activeAnimation.current.forward ||
      activeAnimation.current.backward ||
      activeAnimation.current.left ||
      activeAnimation.current.right
        ? activeAnimation.current.run
          ? animations.current["run"]
          : animations.current["walk"]
        : animations.current["idle"];

    if (prevAction.current !== currAction.current) {
      prevAction.current.fadeOut(0.2);
      currAction.current.reset().fadeIn(0.2).play();
    }

    characterState(delta);
    mixer.current.update(delta);
    boundingBox.current.setFromObject(characterGroup.current); // Update bounding box
  });

  useImperativeHandle(ref, () => ({
    get position() {
      return characterGroup.current.position;
    },
    get quaternion() {
      return characterGroup.current.quaternion;
    },
    get isMoving() {
      return isMoving.current;
    },
  }));

  return (
    <group ref={characterGroup}>
      <Suspense fallback={null}>
        <primitive object={model} />
      </Suspense>
    </group>
  );
});

export default Character;
