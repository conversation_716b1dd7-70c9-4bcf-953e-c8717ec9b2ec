import React, { useRef, useState, useEffect, useMemo, useCallback } from "react";
import PropTypes from "prop-types";
import { useStore } from "../../store/store";
import "../../styles.css";

const TimedText = ({ lang1, lang2, src, subtitleFile, mouthCueFile, isLesson, onMouthCueUpdate }) => {
  const [subtitles, setSubtitles] = useState([]);
  const [mouthCues, setMouthCues] = useState([]);
  const [currentTime, setCurrentTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playbackSpeed, setPlaybackSpeed] = useState(1);
  const [languages, setLanguages] = useState({ lang1, lang2 });
  const audioRef = useRef(null);

  const soundLevel = useStore((state) => state.soundLevel);

  // Fetch and process subtitles
  useEffect(() => {
    fetch(subtitleFile)
      .then((response) => {
        if (!response.ok) {
          throw new Error("Subtitle file fetch failed");
        }
        return response.json();
      })
      .then((data) => {
        const processedData = data.map((segment) => {
          const duration = segment.end - segment.start;
          const words = {};
          const wordTimings = {};

          for (const lang of [languages.lang1, languages.lang2]) {
            const wordsArray = segment.text[lang]?.trim().split(/\s+/) || [];
            const wordTimingInterval = wordsArray.length > 0 ? duration / wordsArray.length : 0;

            words[lang] = wordsArray;
            wordTimings[lang] = wordsArray.map((_, i) => ({
              start: segment.start + i * wordTimingInterval,
              end: segment.start + (i + 1) * wordTimingInterval,
            }));
          }

          return { ...segment, words, wordTimings };
        });
        setSubtitles(processedData);
      })
      .catch((error) => console.error("Failed to load subtitles:", error));
  }, [subtitleFile, languages]);

  // Fetch mouth cues
  useEffect(() => {
    fetch(mouthCueFile)
      .then((response) => {
        if (!response.ok) {
          throw new Error("Mouth cue file fetch failed");
        }
        return response.json();
      })
      .then((data) => setMouthCues(data.mouthCues || []))
      .catch((error) => console.error("Failed to load mouth cues:", error));
  }, [mouthCueFile]);

  // Update current time and notify parent
  useEffect(() => {
    const audioElement = audioRef.current;
    if (!audioElement) return;

    const updateCurrentTime = () => {
      setCurrentTime(audioElement.currentTime);

      if (typeof onMouthCueUpdate === "function") {
        const activeCues = mouthCues.filter(
          (cue) => audioElement.currentTime >= cue.start && audioElement.currentTime <= cue.end
        );
        onMouthCueUpdate(activeCues); // Send active cues to Karola
      }
    };

    const handleEnded = () => setIsPlaying(false);

    audioElement.addEventListener("timeupdate", updateCurrentTime);
    audioElement.addEventListener("ended", handleEnded);

    return () => {
      audioElement.removeEventListener("timeupdate", updateCurrentTime);
      audioElement.removeEventListener("ended", handleEnded);
    };
  }, [mouthCues, onMouthCueUpdate]);

  // Toggle playback
  const handlePlayPause = useCallback(() => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play().catch((error) => console.log("Playback error:", error));
      }
      setIsPlaying((prev) => !prev);
    }
  }, [isPlaying]);

  // Toggle languages
  const toggleLanguages = useCallback(() => {
    setLanguages((prev) => ({
      lang1: prev.lang2,
      lang2: prev.lang1,
    }));
  }, []);

  // Update playback speed
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.playbackRate = playbackSpeed;
    }
  }, [playbackSpeed]);

  // Update audio volume safely by clamping the soundLevel between 0 and 1
  useEffect(() => {
    if (audioRef.current) {
      const clampedVolume = Math.max(0, Math.min(1, soundLevel)); // Ensure the volume is within range
      audioRef.current.volume = clampedVolume;
    }
  }, [soundLevel]);

  // Render subtitles with highlighting
  const renderedSubtitles = useMemo(() => {
    return subtitles.map((segment, segmentIndex) => (
      <div key={segmentIndex} className="text-lang">
        {segment.words[languages.lang1].map((word, wordIndex) => {
          const wordTiming = segment.wordTimings[languages.lang1][wordIndex];
          const isHighlighted =
            currentTime >= wordTiming.start && currentTime < wordTiming.end;
          const wordClass = isHighlighted ? "highlighted-word" : "default-word";

          return (
            <span key={wordIndex} className={wordClass}>
              {word}{" "}
            </span>
          );
        })}
      </div>
    ));
  }, [subtitles, languages, currentTime]);

  return (
    <div className="timed-text-container">
      {/* Language Tabs */}
      <div className="language-tabs">
        <button onClick={toggleLanguages} className="tab-button" aria-label="Toggle Language">
          <img
            src={languages.lang1 === "pl" ? "/flags/pl.webp" : "/flags/gb.webp"}
            alt={`Flag for ${languages.lang1}`}
            className="flag-icon"
          />
        </button>
        <button onClick={toggleLanguages} className="tab-button" aria-label="Toggle Language">
          <img
            src={languages.lang2 === "en" ? "/flags/gb.webp" : "/flags/pl.webp"}
            alt={`Flag for ${languages.lang2}`}
            className="flag-icon"
          />
        </button>
      </div>

      {/* Subtitles */}
      <div className="highlighted-text">{renderedSubtitles}</div>

      {/* Controls */}
      <div className="controls">
        <button onClick={handlePlayPause} className={`audio-button ${isPlaying ? "playing" : ""}`}>
          {isPlaying ? "Pause" : "Play"}
        </button>
        <div className="playback-speed-controls">
          <span>Speed:</span>
          <button onClick={() => setPlaybackSpeed(1)} className={playbackSpeed === 1 ? "active" : ""}>
            1x
          </button>
          <button
            onClick={() => setPlaybackSpeed(0.5)}
            className={playbackSpeed === 0.5 ? "active" : ""}
          >
            0.5x
          </button>
        </div>
      </div>

      {/* Audio Element */}
      <audio ref={audioRef} src={src} preload="metadata" />
    </div>
  );
};

TimedText.propTypes = {
  lang1: PropTypes.string.isRequired,
  lang2: PropTypes.string.isRequired,
  src: PropTypes.string.isRequired,
  subtitleFile: PropTypes.string.isRequired,
  mouthCueFile: PropTypes.string.isRequired,
  isLesson: PropTypes.bool,
  onMouthCueUpdate: PropTypes.func.isRequired,
};

TimedText.defaultProps = {
  isLesson: false,
};

export default TimedText;
