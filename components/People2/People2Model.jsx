import React, { useRef, useEffect } from 'react'
import { useGLTF, useAnimations } from '@react-three/drei'
import * as THREE from 'three'

export function Model(props) {
  const group = useRef()

  const environmentBoundingBox = useRef(new THREE.Box3()) // La bounding box de l'environnement
  const characterBoundingBox = useRef(new THREE.Box3()) // La bounding box du personnage

  const { nodes, materials, animations } = useGLTF('models/old-woman.glb')
  const { actions } = useAnimations(animations, group)

  // Démarre l'animation par défaut
  useEffect(() => {
    if (actions) {
      console.log('Animations disponibles :', actions) // Vérifiez les animations disponibles dans la console
      const defaultAction = actions[Object.keys(actions)[0]] // Récupère la première animation
      if (defaultAction) defaultAction.play() // Joue l'animation si elle existe
    }
  }, [actions])


  return (
        <group ref={group} {...props} dispose={null}>
      <group name="Scene">
        <group
          name="Sketchfab_model"
          position={[-1.076, -0.263, -0.519]}
          rotation={[-Math.PI / 2, 0, 0]}>
          <group name="root">
            <group name="GLTF_SceneRootNode" rotation={[Math.PI / 2, 0, 0]}>
              <group
                name="Object_985007_1906"
                position={[1.048, 1.127, 0.598]}
                rotation={[-Math.PI / 2, 0, -0.737]}
                scale={0.025}>
                <group name="GLTF_created_63">
                  <skinnedMesh
                    name="Object_2138"
                    geometry={nodes.Object_2138.geometry}
                    material={materials['mtl_Female010Skin_001_Color.003']}
                    skeleton={nodes.Object_2138.skeleton}
                  />
                  <primitive object={nodes.GLTF_created_63_rootJoint} />
                </group>
              </group>
            </group>
          </group>
        </group>
      </group>
    </group>
  )
}
 

useGLTF.preload('models/old-woman.glb')
