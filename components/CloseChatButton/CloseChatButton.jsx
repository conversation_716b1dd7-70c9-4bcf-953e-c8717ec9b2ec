import React from "react";
import PropTypes from "prop-types";

const CloseChatButton = ({ onClose }) => {
  return (
    <button
      className="close-chat-button"
      onClick={onClose}
      aria-label="Fermer le chat"
    >
      💤  {/* Icône de fermeture */}
    </button>
  );
};

CloseChatButton.propTypes = {
  onClose: PropTypes.func.isRequired, // Fonction obligatoire
};

// Styles inline (remplaçables par un fichier CSS)

export default CloseChatButton;
