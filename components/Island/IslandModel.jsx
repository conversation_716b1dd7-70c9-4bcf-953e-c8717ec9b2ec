import React from 'react';
import { useGLTF } from '@react-three/drei';

export function Model(props) {

  // Charger le modèle GLB
  const { nodes, materials } = useGLTF('models/environment.glb');

  return (
    <group {...props} dispose={null}>
      <group rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
        <mesh
          castShadow
          receiveShadow
          geometry={nodes.ceiling_ceiling_0001.geometry}
          material={materials['ceiling.001']}
          position={[0, 110.003, 0]}
          rotation={[-Math.PI / 2, 0, 0]}
          scale={100}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={nodes.Frame_Metal_0001.geometry}
          material={materials['Metal.001']}
          position={[0, 110.003, 0]}
          rotation={[-Math.PI / 2, 0, 0]}
          scale={100}
        />
        <mesh
          castShadow
          receiveShadow
          geometry={nodes.Wall_Wall_0001.geometry}
          material={materials['Wall.001']}
          position={[0, 110.003, 0]}
          rotation={[-Math.PI / 2, 0, 0]}
          scale={100}
        />
        </group>
      </group>
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.Plane001.geometry}
        material={materials['Wood_Parquet.002']}
        scale={[-1.111, -1.701, -1.701]}
      />
      <group scale={0.01}>
        <mesh
          castShadow
          receiveShadow
          geometry={nodes.Sphere_Skybox_0.geometry}
          material={materials['Skybox.002']}
          rotation={[-Math.PI / 2, 0, 0]}
          scale={100}
        />
      </group>
    </group>
  )
}


useGLTF.preload('models/environment.glb')
