import { GradientTexture } from "@react-three/drei";
import * as THREE from "three";

export default function Background() {
  return (
    <mesh>
      {/* Utilisation d'une grande sphère pour l'arrière-plan */}
      <sphereGeometry args={[2800, 32, 32]} /> {/* Sphère assez grande pour envelopper la scène */}
      
      <meshBasicMaterial side={THREE.BackSide}>
        {/* Ciel*/}
        <GradientTexture
          stops={[0, 0.3, 0.6, 1]}
          colors={["#1E90FF", "#4682B4", "#87CEEB", "#B0E0E6"]} // Bleu dégradé vers un horizon plus clair
        />
      </meshBasicMaterial>
    </mesh>
  );
}
