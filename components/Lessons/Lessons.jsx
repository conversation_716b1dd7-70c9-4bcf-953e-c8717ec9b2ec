import React from "react";
import PhraseTab from "../../components/PhraseTab/PhraseTab";

const Lessons = () => {
  const lessons = [
    {
      id: 1,
      title: "Lesson 1: Basics",
      description: `
        Welcome to Lesson 1: Basics! This lesson is designed to help you learn fundamental Polish expressions used in everyday conversations.
        These phrases are the foundation for starting to interact in Polish. You will learn how to greet someone, express gratitude, and use
        polite phrases like "please" and "thank you." Additionally, you will practice essential responses like "yes" and "no."
        Phrases include: Hello, Thank you, Please, Yes, No, Good morning, Good night, Excuse me, How are you, and I’m fine, thank you.
      `,
      image: "/images/lessons/lesson1.webp",
      phrases: [
        { id: 1, text: { en: "Hello", pl: "<PERSON><PERSON><PERSON><PERSON>" }, audio: "/audio/lessons/lesson1/Cześ<PERSON>.mp3", subtitles: "/subtitles/lessons/lesson1/Cześć.json" },
        { id: 2, text: { en: "Thank you", pl: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>" }, audio: "/audio/lessons/lesson1/Dziękuję.mp3", subtitles: "/subtitles/lessons/lesson1/Dziękuję.json" },
        { id: 3, text: { en: "Please / You’re welcome", pl: "Proszę" }, audio: "/audio/lessons/lesson1/Proszę.mp3", subtitles: "/subtitles/lessons/lesson1/Proszę.json" },
        { id: 4, text: { en: "Yes", pl: "Tak" }, audio: "/audio/lessons/lesson1/Tak.mp3", subtitles: "/subtitles/lessons/lesson1/Tak.json" },
        { id: 5, text: { en: "No", pl: "Nie" }, audio: "/audio/lessons/lesson1/Nie.mp3", subtitles: "/subtitles/lessons/lesson1/Nie.json" },
        { id: 6, text: { en: "Good morning", pl: "Dzień dobry" }, audio: "/audio/lessons/lesson1/Dzień_dobry.mp3", subtitles: "/subtitles/lessons/lesson1/Dzień_dobry.json" },
        { id: 7, text: { en: "Good night", pl: "Dobranoc" }, audio: "/audio/lessons/lesson1/Dobranoc.mp3", subtitles: "/subtitles/lessons/lesson1/Dobranoc.json" },
        { id: 8, text: { en: "Excuse me", pl: "Przepraszam" }, audio: "/audio/lessons/lesson1/Przepraszam.mp3", subtitles: "/subtitles/lessons/lesson1/Przepraszam.json" },
        { id: 9, text: { en: "How are you?", pl: "Jak się masz?" }, audio: "/audio/lessons/lesson1/Jak_się_masz.mp3", subtitles: "/subtitles/lessons/lesson1/Jak_się_masz.json" },
        { id: 10, text: { en: "I’m fine, thank you", pl: "Dobrze, dziękuję" }, audio: "/audio/lessons/lesson1/Dobrze_dziękuję.mp3", subtitles: "/subtitles/lessons/lesson1/Dobrze_dziękuję.json" },
      ],
    },
  ];

  // Helper function to render the description with clickable words and multi-word phrases
  const renderDescription = (description, phrases) => {
    const phraseMap = new Map(
      phrases.map((phrase) => [phrase.text.en.toLowerCase(), phrase.audio])
    );

    // Sort phrases by length (descending) to match longer phrases first
    const sortedPhrases = [...phraseMap.keys()].sort((a, b) => b.length - a.length);

    // Highlight and replace matched phrases
    let remainingDescription = description;
    const elements = [];

    while (remainingDescription.length > 0) {
      let matched = false;

      for (const phrase of sortedPhrases) {
        if (remainingDescription.toLowerCase().startsWith(phrase)) {
          const audioSrc = phraseMap.get(phrase);

          elements.push(
            <span
              key={elements.length}
              className="clickable-word"
              onClick={() => new Audio(audioSrc).play()}
              style={{ color: "#007BFF", cursor: "pointer", fontWeight: "bold" }}
            >
              {remainingDescription.slice(0, phrase.length)}
            </span>
          );

          remainingDescription = remainingDescription.slice(phrase.length).trimStart();
          matched = true;
          break;
        }
      }

      // If no phrase matches, take the next character as plain text
      if (!matched) {
        elements.push(
          <span key={elements.length}>
            {remainingDescription[0]}
          </span>
        );
        remainingDescription = remainingDescription.slice(1);
      }
    }

    return elements;
  };

  return (
    <div className="lessons-fullscreen">
      <div className="lessons-container">
        <h2 className="lessons-title">Beginner Language Lessons</h2>
        {lessons.map((lesson) => (
          <div key={lesson.id} className="lesson-card">
            <h3 className="lesson-title">{lesson.title}</h3>
            <p className="lesson-description">
              {renderDescription(lesson.description, lesson.phrases)}
            </p>
            <div className="lesson-image-container">
              <img
                src={lesson.image}
                alt={`${lesson.title} illustration`}
                className="lesson-image"
              />
            </div>
            <div className="phrases">
              {lesson.phrases.map((phrase) => (
                <div key={phrase.id} className="phrase-card">
                  <div className="phrase-text">
                    <p>{phrase.text.pl}</p> {/* Displays Polish text by default */}
                  </div>
                  <PhraseTab phrase={phrase} />
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Lessons;
