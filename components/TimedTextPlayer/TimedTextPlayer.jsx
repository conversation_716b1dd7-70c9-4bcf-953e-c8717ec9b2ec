import React, { useState, useEffect, useRef } from "react";

// Sample phrases with audio and timed text
const phrases = [
  {
    en: "Did someone say magic? Oh wait, that’s just my breakfast talking.",
    pl: "<PERSON><PERSON><PERSON> mówił o magii? A nie, to tylko moje ś<PERSON>ie mówi.",
    audio: "/audio/magic_breakfast.mp3",
    subtitles: "/subtitles/magic_breakfast.json",
  },
  // Other phrases...
];

const TimedTextPlayer = () => {
  const [currentPhrase, setCurrentPhrase] = useState(null);
  const [timedText, setTimedText] = useState([]);
  const [currentWordIndex, setCurrentWordIndex] = useState(0);
  const [language, setLanguage] = useState("en"); // Default language
  const audioRef = useRef(null);

  // Load subtitles from JSON
  const loadSubtitles = async (subtitlesPath) => {
    try {
      const response = await fetch(subtitlesPath);
      const subtitles = await response.json();
      setTimedText(subtitles[language]); // Dynamically set based on language
    } catch (error) {
      console.error("Error loading subtitles:", error);
    }
  };

  // Play a random phrase
  const playRandomPhrase = () => {
    const randomPhrase = phrases[Math.floor(Math.random() * phrases.length)];
    setCurrentPhrase(randomPhrase);

    // Load subtitles
    loadSubtitles(randomPhrase.subtitles);

    // Play audio
    if (audioRef.current) {
      audioRef.current.src = randomPhrase.audio;
      audioRef.current.play().catch((error) => console.error("Audio play error:", error));
    }
  };

  // Update the current word index based on audio time
  useEffect(() => {
    const handleTimeUpdate = () => {
      const currentTime = audioRef.current?.currentTime || 0;
      const activeWordIndex = timedText.findIndex(
        (text) => currentTime >= text.start && currentTime < text.end
      );
      if (activeWordIndex !== -1) {
        setCurrentWordIndex(activeWordIndex);
      }
    };

    if (audioRef.current) {
      audioRef.current.addEventListener("timeupdate", handleTimeUpdate);
    }

    return () => {
      if (audioRef.current) {
        audioRef.current.removeEventListener("timeupdate", handleTimeUpdate);
      }
    };
  }, [timedText]);

  // Handle language switch
  const switchLanguage = () => {
    setLanguage((prev) => (prev === "en" ? "pl" : "en"));
  };

  return (
    <div className="timed-text-player">
      {/* Display the current phrase's text */}
      <div className="timed-text">
        {timedText.map((text, index) => (
          <span
            key={index}
            className={index === currentWordIndex ? "highlighted-word" : "default-word"}
          >
            {text.word}{" "}
          </span>
        ))}
      </div>

      {/* Audio element */}
      <audio ref={audioRef} />

      {/* Controls */}
      <div className="controls">
        <button onClick={playRandomPhrase}>Play Random Phrase</button>
        <button onClick={switchLanguage}>
          Switch to {language === "en" ? "Polish" : "English"}
        </button>
      </div>
    </div>
  );
};

export default TimedTextPlayer;
