import { useFrame } from "@react-three/fiber";
import { useRef } from "react";

export default function Lights() {
  const vibrantRedColor = 0xff4500; // Rouge vif
  const deepGreenColor = 0x228b22; // Vert profond
  const vibrantBlueColor = 0x87ceeb; // Bleu ciel vif

  const lights = [
    { position: [10, 15, 10], color: vibrantRedColor, intensity: 2.0, decay: 2 },
    { position: [15, 10, 10], color: deepGreenColor, intensity: 1.8, decay: 1.5 },
    { position: [0, 20, 15], color: vibrantBlueColor, intensity: 1.5, decay: 2 },
  ];

  const directionalLightRef = useRef();

  // Animation légère pour la lumière directionnelle (exemple)
  useFrame(({ clock }) => {
    if (directionalLightRef.current) {
      const time = clock.getElapsedTime();
      directionalLightRef.current.position.x = Math.sin(time) * 10;
      directionalLightRef.current.position.z = Math.cos(time) * 10;
    }
  });

  return (
    <>
      
    


      {/* Lumière ambiante douce */}
      <ambientLight intensity={1} color={0xffffff} />
    </>
  );
}
