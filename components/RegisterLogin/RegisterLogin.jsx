import React, { useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import { useNavigate } from 'react-router-dom';

// Base API URL for WordPress
const API_URL = 'https://feelpolska.com/wp-json';

// Reusable Input Component
const FormInput = React.memo(function FormInput({
  label,
  type,
  id,
  name,
  placeholder,
  value,
  onChange,
  error,
}) {
  return (
    <div className="form-group">
      <label htmlFor={id}>{label}</label>
      <input
        type={type}
        id={id}
        name={name}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        required
        className={error ? 'input-error' : ''}
      />
      {error && <p className="error-text">{error}</p>}
    </div>
  );
});

FormInput.propTypes = {
  label: PropTypes.string.isRequired,
  type: PropTypes.string.isRequired,
  id: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  placeholder: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  error: PropTypes.string,
};



const validateLoginData = (data) => {
  const errors = {};
  if (!data.username.trim()) errors.username = 'Username is required.';
  if (data.password.length < 6) errors.password = 'Password must be at least 6 characters.';
  return errors;
};



// LoginForm Component
const LoginForm = React.memo(function LoginForm({ onSuccess }) {
  const navigate = useNavigate();
  const [loginData, setLoginData] = useState({ username: '', password: '' });
  const [errors, setErrors] = useState({});
  const [message, setMessage] = useState('');

  const handleInputChange = useCallback((e) => {
    const { name, value } = e.target;
    setLoginData((prev) => ({ ...prev, [name]: value }));
  }, []);

  const handleSubmit = useCallback(
    async (e) => {
      e.preventDefault();
      const formErrors = validateLoginData(loginData);
      if (Object.keys(formErrors).length === 0) {
        try {
          const response = await fetch(`${API_URL}/jwt-auth/v1/token`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(loginData),
          });
          const data = await response.json();
          if (response.ok) {
            localStorage.setItem('token', data.token);
            setMessage('Login successful!');
            setLoginData({ username: '', password: '' });
  
            // Reload the page before redirecting
            if (onSuccess) {
              onSuccess();
            }
            window.location.reload(); // Reload the current page
            setTimeout(() => {
              navigate('./'); // Redirect to the home page
            }, 100);
          } else {
            setMessage(data.message || 'Login failed.');
          }
        } catch (error) {
          setMessage('An error occurred: ' + error.message);
        }
      } else {
        setErrors(formErrors);
      }
    },
    [loginData, onSuccess, navigate]
  );
  

  return (
    <form className="form-container" onSubmit={handleSubmit}>
      <p>Login</p>
      <FormInput
        label="Username:"
        type="text"
        id="username"
        name="username"
        placeholder="Enter your username"
        value={loginData.username}
        onChange={handleInputChange}
        error={errors.username}
      />
      <FormInput
        label="Password:"
        type="password"
        id="password"
        name="password"
        placeholder="Enter your password"
        value={loginData.password}
        onChange={handleInputChange}
        error={errors.password}
      />
      <button type="submit" className="login-btn">Login</button>
      {message && <p className="form-message">{message}</p>}
    </form>
  );
});

LoginForm.propTypes = {
  onSuccess: PropTypes.func,
};

// RegisterLoginTabs Component
const RegisterLoginTabs = React.memo(function RegisterLoginTabs() {
  const [activeTab, setActiveTab] = useState('login');
  const [isVisible, setIsVisible] = useState(true);

  const handleSuccess = useCallback(() => {
    setIsVisible(false);
  }, []);

  if (!isVisible) return null;

  return (
    <div className="tabs-container">
      <div className="tabs-header">

    
      </div>
      <div className="tabs-content">
        {activeTab === 'login' ? (
          <LoginForm onSuccess={handleSuccess} />
        ) : (
          <RegisterForm onSuccess={handleSuccess} />
        )}
      </div>
    </div>
  );
});

export default RegisterLoginTabs;
