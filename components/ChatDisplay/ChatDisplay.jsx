import React, { useState, useEffect, useRef, useCallback } from "react";
import { useChat } from "../../hooks/useChat";
import "../../styles.css";

const MAX_HISTORY_LENGTH = 50;

/** Hook personnalisé pour gérer l'historique du chat */
const useChatHistory = () => {
  const loadHistory = useCallback(() => {
    try {
      const saved = localStorage.getItem("chatHistory");
      return saved ? JSON.parse(saved) : [];
    } catch (err) {
      console.error("Error loading chat history:", err);
      return [];
    }
  }, []);

  const saveHistory = useCallback((hist) => {
    try {
      const limited = hist.slice(-MAX_HISTORY_LENGTH);
      localStorage.setItem("chatHistory", JSON.stringify(limited));
    } catch (err) {
      console.error("Error saving chat history:", err);
    }
  }, []);

  const clearHistory = useCallback(() => {
    try {
      localStorage.removeItem("chatHistory");
    } catch (err) {
      console.error("Error clearing chat history:", err);
    }
  }, []);

  const [history, setHistoryState] = useState(() => loadHistory());

  useEffect(() => {
    saveHistory(history);
  }, [history, saveHistory]);

  const setHistory = useCallback((updater) => {
    setHistoryState((prev) => {
      const updated = typeof updater === "function" ? updater(prev) : updater;
      return updated;
    });
  }, []);

  return [history, setHistory, clearHistory];
};

const ChatDisplay = ({ onClose }) => {
  const { messages, chat, loading } = useChat();
  const [userInput, setUserInput] = useState("");
  const [history, setHistory, clearHistory] = useChatHistory();
  const [visibleMessages, setVisibleMessages] = useState([]);
  const [currentPlayingIndex, setCurrentPlayingIndex] = useState(null);
  const [highlightedWordIndex, setHighlightedWordIndex] = useState(0);
  const [audioDuration, setAudioDuration] = useState(0);
  const [isAudioPaused, setIsAudioPaused] = useState(false);

  const audioRef = useRef(null);
  const messagesEndRef = useRef(null);
  const seenMessagesRef = useRef(new Set(history.map((m) => m.text)));

  // Interval pour avancer les mots sans audio
  const readingIntervalRef = useRef(null);

  // Clear chat history on component unmount or chat close
  useEffect(() => {
    const handleUnload = () => clearHistory();
    window.addEventListener("beforeunload", handleUnload);
    return () => {
      handleUnload(); 
      window.removeEventListener("beforeunload", handleUnload);
    };
  }, [clearHistory]);

  // Chargement des nouveaux messages dans l'historique
  useEffect(() => {
    if (messages.length === 0) return;
    let added = false;
    const newMsgs = messages.reduce((acc, msg) => {
      if (!seenMessagesRef.current.has(msg.text)) {
        seenMessagesRef.current.add(msg.text);
        acc.push({ ...msg, isUser: false });
        added = true;
      }
      return acc;
    }, []);
    if (added && newMsgs.length > 0) {
      setHistory((prev) => [...prev, ...newMsgs]);
    }
  }, [messages, setHistory]);

  // Affichage progressif des messages
  useEffect(() => {
    let timer;
    if (visibleMessages.length < history.length) {
      const nextMessage = history[visibleMessages.length];
      const delay = nextMessage.isUser ? 0 : 1000;
      timer = setTimeout(() => {
        setVisibleMessages((prev) => [...prev, nextMessage]);
      }, delay);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [history, visibleMessages]);

  // Scroll automatique vers le bas
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [visibleMessages, currentPlayingIndex]);

  // Soumission du message utilisateur
  const handleSubmit = useCallback(
    (e) => {
      e.preventDefault();
      const trimmed = userInput.trim();
      if (trimmed) {
        const newMessage = {
          text: trimmed,
          isUser: true,
          timestamp: new Date().toISOString(),
        };

        if (!seenMessagesRef.current.has(trimmed)) {
          seenMessagesRef.current.add(trimmed);
          setHistory((prev) => [...prev, newMessage]);
        }

        chat(trimmed);
        setUserInput("");
      }
    },
    [chat, userInput, setHistory]
  );

  // Lecture de l'audio
  const playAudio = useCallback((audioSrc, index) => {
    if (readingIntervalRef.current) {
      clearInterval(readingIntervalRef.current);
      readingIntervalRef.current = null;
    }

    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      audioRef.current.src = audioSrc;
      setCurrentPlayingIndex(index);
      setHighlightedWordIndex(0);
      setIsAudioPaused(false);
      audioRef.current
        .play()
        .catch((err) => console.error("Error playing audio:", err));
    }
  }, []);

  // Pause / Resume audio
  const toggleAudioPause = useCallback(() => {
    if (audioRef.current) {
      if (audioRef.current.paused) {
        audioRef.current.play().catch((err) => console.error("Play error:", err));
        setIsAudioPaused(false);
      } else {
        audioRef.current.pause();
        setIsAudioPaused(true);
      }
    }
  }, []);

  // Mise à jour du mot surligné avec l'audio
  const handleTimeUpdate = useCallback(() => {
    if (audioRef.current && currentPlayingIndex !== null && audioDuration > 0) {
      const message = visibleMessages[currentPlayingIndex];
      if (message && message.text) {
        const words = message.text.split(" ");
        const totalWords = words.length;
        const currentTime = audioRef.current.currentTime;
        const newIndex = Math.min(
          totalWords - 1,
          Math.floor((currentTime / audioDuration) * totalWords)
        );
        if (newIndex !== highlightedWordIndex) {
          setHighlightedWordIndex(newIndex);
        }
      }
    }
  }, [currentPlayingIndex, highlightedWordIndex, visibleMessages, audioDuration]);

  // Durée de l'audio chargée
  const handleLoadedMetadata = useCallback(() => {
    if (audioRef.current) {
      setAudioDuration(audioRef.current.duration);
    }
  }, []);

  // Fin de l'audio
  const handleAudioEnded = useCallback(() => {
    setHighlightedWordIndex(0);
    setCurrentPlayingIndex(null);
    setIsAudioPaused(false);
    // Reprendre la lecture visuelle si souhaité, sinon laissez tel quel
  }, []);

  // Lorsque de nouveaux messages apparaissent, lancer l'animation sans audio
  useEffect(() => {
    // Si un message n'a pas d'audio en cours de lecture, on lance l'effet de mise en évidence
    // pour le dernier message bot.
    if (currentPlayingIndex === null && visibleMessages.length > 0) {
      const lastIndex = visibleMessages.length - 1;
      const lastMessage = visibleMessages[lastIndex];

      if (!lastMessage.isUser && lastMessage.text) {
        setHighlightedWordIndex(0);

        const wordsCount = lastMessage.text.split(" ").length;

        if (readingIntervalRef.current) {
          clearInterval(readingIntervalRef.current);
        }

        readingIntervalRef.current = setInterval(() => {
          setHighlightedWordIndex((prev) => {
            const nextIndex = prev + 1;
            if (nextIndex >= wordsCount) {
              clearInterval(readingIntervalRef.current);
              readingIntervalRef.current = null;
            }
            return nextIndex;
          });
        }, 500); // Intervalle pour faire avancer d'un mot toutes les 500ms
      }
    }
  }, [visibleMessages, currentPlayingIndex]);

  // Rendu du texte avec mise en évidence progressive des mots
  const renderMessageText = useCallback(
    (message, highlightIndex) => {
      if (!message || !message.text) return null;
      const words = message.text.split(" ");
      return (
        <p>
          {words.map((word, idx) => {
            let className = "";
            // Mots déjà "vus"
            if (idx < highlightIndex) {
              className = "spoken-word";
            } 
            // Mot en cours
            else if (idx === highlightIndex) {
              className = "highlighted-word";
            }

            return (
              <span
                key={idx}
                className={className}
                style={{
                  transition: "all 0.3s ease",
                }}
              >
                {word}
                {idx < words.length - 1 && " "}
              </span>
            );
          })}
        </p>
      );
    },
    []
  );

  // Barre de progression audio (optionnelle)
  const renderProgressBar = useCallback(() => {
    if (currentPlayingIndex === null || !audioRef.current || audioDuration === 0) {
      return null;
    }
    const currentTime = audioRef.current.currentTime;
    const progress = (currentTime / audioDuration) * 100;
    return (
      <div style={{ width: "100%", background: "#eee", height: "5px", marginTop: "5px" }}>
        <div
          style={{
            width: `${progress}%`,
            background: "#2196F3",
            height: "100%",
            transition: "width 0.25s"
          }}
        ></div>
      </div>
    );
  }, [currentPlayingIndex, audioDuration]);

  return (
    <div className="chat-container">
      <div className="chat-header"></div>
      <div className="chat-messages">
        {visibleMessages.map((message, index) => {
          const isPlaying = currentPlayingIndex === index;
          // Si isPlaying on suit l'audio, sinon on utilise l'index global actuel
          const wordIndex = isPlaying ? highlightedWordIndex : (index === visibleMessages.length - 1 ? highlightedWordIndex : 0);
          return (
            <div
              key={index}
              id={`message-${index}`}
              className={`chat-message ${message.isUser ? "user" : "bot"}`}
              style={{ position: "relative", marginBottom: "15px" }}
            >
              {renderMessageText(message, wordIndex)}
              {message.audio && (
                <div style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                  <button
                    onClick={() =>
                      playAudio(`data:audio/mp3;base64,${message.audio}`, index)
                    }
                    className="play-audio-button"
                  >
                    ▶️
                  </button>
                  {isPlaying && (
                    <button onClick={toggleAudioPause} className="pause-audio-button">
                      {isAudioPaused ? "▶️" : "⏸"}
                    </button>
                  )}
                </div>
              )}
              {isPlaying && renderProgressBar()}
            </div>
          );
        })}
        <div ref={messagesEndRef} />
      </div>

      <form onSubmit={handleSubmit} className="chat-form">
        <input
          type="text"
          value={userInput}
          onChange={(e) => setUserInput(e.target.value)}
          placeholder="Type your message..."
          disabled={loading}
          className="chat-input"
        />
        <button type="submit" disabled={loading} className="chat-submit">
          {loading ? "Thinking" : "Send"}
        </button>
      </form>

      <audio
        ref={audioRef}
        style={{ display: "none" }}
        onLoadedMetadata={handleLoadedMetadata}
        onTimeUpdate={handleTimeUpdate}
        onEnded={handleAudioEnded}
      />
    </div>
  );
};

const ChatContainer = () => {
  const [isChatOpen, setIsChatOpen] = useState(true);

  useEffect(() => {
    setIsChatOpen(true);
  }, []);

  return isChatOpen ? <ChatDisplay onClose={() => setIsChatOpen(false)} /> : null;
};

export default ChatContainer;
