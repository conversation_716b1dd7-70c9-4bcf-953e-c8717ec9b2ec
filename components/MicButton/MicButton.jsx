// MicButton.jsx
import React, {
  useState,
  useEffect,
  useRef,
  useCallback
} from "react";
import { ReactMic } from "react-mic";
import PropTypes from "prop-types";
import axiosInstance from "../../axiosInstance";
// On importe le contexte d'auth depuis votre AuthContext
import { useAuth } from "../../AuthContext.jsx";
import LoginDialog from "../LoginDialog/LoginDialog.jsx";
import ErrorModal from "../ErrorModal/ErrorModal.jsx";

// Log helper
const log = (...args) => {
  if (process.env.NODE_ENV !== "production") {
    console.log(...args);
  }
};

const MicButton = ({ onSend, disabled, onError }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [showLoginDialog, setShowLoginDialog] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [errorCode, setErrorCode] = useState(null);

  const recorderTimeout = useRef(null);
  const reactMicRef = useRef(null);

  // Récupération du contexte d'auth
  const { user, loading, purchaseCredits } = useAuth();

  useEffect(() => {
    // Nettoyage du timeout si le composant se démonte
    return () => {
      if (recorderTimeout.current) {
        clearTimeout(recorderTimeout.current);
      }
    };
  }, []);

  const stopTimeout = useCallback(() => {
    if (recorderTimeout.current) {
      clearTimeout(recorderTimeout.current);
      recorderTimeout.current = null;
    }
  }, []);

  const startRecording = useCallback(() => {
    // Vérifie si l’utilisateur est connecté
    if (!user && !loading) {
      setShowLoginDialog(true);
      return;
    }
    stopTimeout();
    setIsRecording(true);
    setIsListening(true);
    setErrorMessage(null);
    setErrorCode(null);
    log("Recording started...");
  }, [user, loading, stopTimeout]);

  const stopRecording = useCallback(() => {
    setIsRecording(false);
    setIsListening(false);
    stopTimeout();
    log("Recording stopped...");
  }, [stopTimeout]);

  const onData = useCallback(() => {
    // Réinitialise le timeout à chaque réception de data
    stopTimeout();
    recorderTimeout.current = setTimeout(() => {
      log("No data received. Stopping recording...");
      stopRecording();
    }, 200);
  }, [stopRecording, stopTimeout]);

  const onStop = useCallback(
    async (recordedBlob) => {
      stopTimeout();
      setIsUploading(true);

      // Vérifie si l’utilisateur est connecté
      if (!user) {
        log("User not authenticated. Please log in.");
        setShowLoginDialog(true);
        setIsUploading(false);
        return;
      }

      try {
        log("Uploading audio...");
        const formData = new FormData();
        formData.append("file", recordedBlob.blob, "audio.mp3");

        // axiosInstance est censé contenir la configuration 
        // de l'en-tête Authorization (via intercepteurs ou contexte)
        const response = await axiosInstance.post("/upload-audio", formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        });

        const transcribedText = response.data.text;
        log("Transcribed Text:", transcribedText);

        if (onSend) {
          onSend(transcribedText);
        }

        setIsUploading(false);

        // Redémarrer l'enregistrement si on souhaite enregistrer en continu
        if (isListening) {
          log("Restarting recording...");
          startRecording();
        }
      } catch (error) {
        log("Error transcribing audio: ", error);

        // Détermine le message d'erreur
        let msg = "Failed to upload or transcribe audio. Please try again.";
        let code = error.response?.status || 500;

        if (code === 402) {
          msg = error.response?.data?.message || "Payment is required to proceed.";
        }

        setErrorCode(code);
        setErrorMessage(msg);

        if (onError) {
          onError({ errorCode: code, errorMessage: msg });
        }
        setIsUploading(false);
      }
    },
    [onSend, onError, isListening, user, startRecording, stopTimeout]
  );

  const closeLoginDialog = useCallback(() => {
    setShowLoginDialog(false);
  }, []);

  const closeErrorModal = useCallback(() => {
    setErrorMessage(null);
    setErrorCode(null);
  }, []);

  return (
    <div className="mic-button-container">
      {showLoginDialog && <LoginDialog onClose={closeLoginDialog} />}
      {errorCode && (
        <ErrorModal
          errorCode={errorCode}
          errorMessage={errorMessage}
          onClose={closeErrorModal}
          onPurchaseCredits={() => {
            closeErrorModal();
            purchaseCredits(10); // Exemple d'achat de crédits
          }}
        />
      )}

      {isUploading && <div className="upload-indicator"></div>}

      {errorMessage && !errorCode && (
        <div className="error-message">{errorMessage}</div>
      )}

      <ReactMic
        ref={reactMicRef}
        record={isRecording}
        onStop={onStop}
        onData={onData}
        className="hidden"
        style={{
          display: "none",
          width: 0,
          height: 0,
          overflow: "hidden",
        }}
        mimeType="audio/mp3"
      />

      <button
        className={`mic-button ${isListening ? "active" : ""}`}
        onClick={isListening ? stopRecording : startRecording}
        disabled={disabled || isUploading}
        aria-pressed={isListening}
      >
        {isListening ? (
          // Icône micro actif
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 512 512"
            fill="currentColor"
            stroke="currentColor"
            className="mic-icon"
          >
            <path d="M468.53 236.03H486v39.94h-17.47v-39.94zm-34.426 51.634h17.47v-63.328h-17.47v63.328zm-33.848 32.756h17.47V191.58h-17.47v128.84zm-32.177 25.276h17.47V167.483h-17.47v178.17zm-34.448-43.521h17.47v-92.35h-17.47v92.35zm-34.994 69.879h17.47v-236.06h-17.525v236.06zM264.2 405.9h17.47V106.1H264.2V405.9zm-33.848-46.284h17.47V152.383h-17.47v207.234zm-35.016-58.85h17.47v-87.35h-17.47v87.35zm-33.847-20.823h17.47V231.98h-17.47v48.042zm-33.848 25.66h17.47v-99.24h-17.47v99.272zm-33.302 48.04h17.47V152.678H94.34v201zm-33.847-30.702h17.47V187.333h-17.47v135.642zM26 287.664h17.47v-63.328H26v63.328z" />
          </svg>
        ) : (
          // Icône micro inactif
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="mic-icon"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 18.75a6 6 0 0 0 6-6v-1.5m-6 7.5a6 6 0 0 1-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 0 1-3-3V4.5a3 3 0 1 1 6 0v8.25a3 3 0 0 1-3 3Z"
            />
          </svg>
        )}
      </button>
    </div>
  );
};

MicButton.propTypes = {
  onSend: PropTypes.func,
  disabled: PropTypes.bool,
  onError: PropTypes.func,
};

MicButton.defaultProps = {
  onSend: null,
  disabled: false,
  onError: null,
};

export default MicButton;
