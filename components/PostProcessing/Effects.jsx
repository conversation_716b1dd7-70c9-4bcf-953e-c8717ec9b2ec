import React, { useRef } from "react";
import { EffectComposer, Outline, Vignette, Bloom } from "@react-three/postprocessing";
import { BlendFunction } from "postprocessing";
import * as THREE from "three";

export function Effects() {
  const lightRef = useRef();

  return (
    <>
      {/* Composer pour appliquer les effets de post-traitement */}
      <EffectComposer multisampling={8} autoClear={true}>
        
        {/* Contours pour un effet de dessin animé */}
        <Outline
          blendFunction={BlendFunction.SCREEN}
          edgeStrength={1.2} // Contours plus doux
          pulseSpeed={0.0}
          visibleEdgeColor={0x000000}  // Noir pour les contours visibles
          hiddenEdgeColor={0x000000}   // Noir pour les contours cachés
        />

        {/* Bloom ajusté pour ne pas trop briller */}
        <Bloom
          intensity={0.0004} // Réduit l'intensité du bloom pour un effet subtil
          width={800}     // Largeur du bloom
          height={800}    // Hauteur du bloom
          kernelSize={5}  // Taille du noyau pour l'effet de flou
        />

      
        <Vignette
          eskil={false}
          offset={0.3}
          darkness={0.2} // Réduction pour un effet plus léger
        />
    
      </EffectComposer>

      {/* Lumière directionnelle pour l'effet Toon */}
      <directionalLight
        ref={lightRef}
        position={[5, 500, 10]}
        intensity={0.1} // Ajustement de l'intensité pour éviter l'excès de lumière
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
        shadow-camera-far={50}
        shadow-camera-left={-10}
        shadow-camera-right={10}
        shadow-camera-top={10}
        shadow-camera-bottom={-10}
      />
    </>
  );
}
