import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import ConnectionTime from "../ConnectionTime/ConnectionTime";

const UserDashboard = () => {
  const navigate = useNavigate();
  const [userData, setUserData] = useState({ username: null, email: null });
  const [credits, setCredits] = useState(null);
  const [role, setRole] = useState(null);
  const [progress, setProgress] = useState([]);
  const [creditLogs, setCreditLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);

  const API_URL = import.meta.env.VITE_API_URL || "https://feelpolska.com/wp-json";

  const fetchUserData = async () => {
    setLoading(true);
    setError(null);

    const token = localStorage.getItem("token");
    if (!token) {
      setError("Token d'authentification manquant.");
      setLoading(false);
      return;
    }

    try {
      const response = await fetch(`${API_URL}/wp/v2/users/me`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorMessage = `Erreur API: ${response.status}`;
        throw new Error(errorMessage);
      }

      const data = await response.json();
      setUserData({ username: data.name, email: data.email });
      setRole(data.roles?.[0] || "user"); // Récupérer le rôle principal
    } catch (err) {
      console.error("Erreur lors du chargement des données utilisateur:", err);
      setError("Erreur lors du chargement des données utilisateur.");
    } finally {
      setLoading(false);
    }
  };

  const handleBuyCredits = async (amount) => {
    setLoading(true);
    setError(null);
    setSuccessMessage(null);

    const token = localStorage.getItem("token");
    if (!token) {
      setError("Token d'authentification manquant.");
      setLoading(false);
      return;
    }

    try {
      const response = await fetch(`${API_URL}/custom/credits/add`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ amount }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Erreur lors de l'achat de crédits.");
      }

      const data = await response.json();
      setCredits(data.credits);
      setSuccessMessage(`Vous avez acheté ${amount} crédits avec succès!`);
    } catch (err) {
      console.error(err);
      setError("Erreur lors de l'achat de crédits. Veuillez réessayer.");
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem("token");
    navigate("/login");
  };

  useEffect(() => {
    fetchUserData();
  }, []);

  return (
    <div className="dashboard-fullscreen">
      <div className="dashboard-container">
        <h2>Bienvenue sur ton Dashboard, {userData.username || "Chargement..."}!</h2>

        {error && <p className="error-message">{error}</p>}
        {successMessage && <p className="success-message">{successMessage}</p>}

        <section className="user-info">
          <h3>Informations Utilisateur</h3>
          <p><strong>Email:</strong> {userData.email || "Chargement..."}</p>
          <p><strong>Rôle:</strong> {role || "Chargement..."}</p>
          <button
            onClick={() => handleBuyCredits(500)}
            disabled={loading}
          >
            {loading ? "Achat en cours..." : "Acheter 500 crédits"}
          </button>
        </section>

        <ConnectionTime />

        <section className="user-progress">
          <h3>Progrès</h3>
          {progress.length > 0 ? (
            <ul>
              {progress.map((item) => (
                <li key={item.id}>
                  Module: {item.module} - Score: {item.score}
                </li>
              ))}
            </ul>
          ) : (
            <p>Aucun progrès enregistré.</p>
          )}
        </section>

        <section className="credit-logs">
          <h3>Historique des crédits</h3>
          {creditLogs.length > 0 ? (
            <ul>
              {creditLogs.map((log) => (
                <li key={log.id}>
                  Changement: {log.change} - Raison: {log.reason}
                </li>
              ))}
            </ul>
          ) : (
            <p>Aucun historique de crédits disponible.</p>
          )}
        </section>

        <section className="user-permissions">
          <h3>Fonctionnalités Disponibles</h3>
          {role === "administrator" && <p>Accès complet à toutes les fonctionnalités.</p>}
          {role === "premium" && <p>Accès aux fonctionnalités premium.</p>}
          {role === "user" && <p>Accès aux fonctionnalités de base.</p>}
          {!role && <p>Rôle non défini. Veuillez contacter l'administrateur.</p>}
        </section>
        <button className="logout-button" onClick={handleLogout}>
          Déconnexion
        </button>
      </div>
    </div>
  );
};

export default UserDashboard;
