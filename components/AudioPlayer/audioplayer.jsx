// components/AudioPlayer/AudioPlayer.js
import React, { useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useStore } from "../../../store/store"; // Adjust the path as necessary

const AudioPlayer = ({ src }) => {
  const audioRef = useRef(null);
  const soundLevel = useStore((state) => state.soundLevel); // Access sound level from Zustand store

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = soundLevel / 100; // Normalize volume to range 0 - 1
    }
  }, [soundLevel]);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.play();
    }
  }, [src]);

  return <audio ref={audioRef} src={src} controls preload="metadata" />;
};

AudioPlayer.propTypes = {
  src: PropTypes.string.isRequired,
};

export default AudioPlayer;
