import React, { useState, useEffect, useRef } from "react";
import "../../styles.css";
import { RiArrowRightLine } from "react-icons/ri";
import { useStore } from "../../store/store";

const CustomCursor = React.memo(() => {
  const cursorType = useStore((store) => store.cursorType);
  const [mousePosition, setMousePosition] = useState({ x: 400, y: 400 });
  const requestRef = useRef(null);

  const onMouseMove = (e) => {
    const { pageX: x, pageY: y } = e;
    if (requestRef.current) {
      cancelAnimationFrame(requestRef.current);
    }
    requestRef.current = requestAnimationFrame(() => {
      setMousePosition({ x, y });
    });
  };

  useEffect(() => {
    document.addEventListener("mousemove", onMouseMove);
    return () => {
      document.removeEventListener("mousemove", onMouseMove);
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    };
  }, []);

  return (
    <div
      className={`cursor ${
        cursorType === "custom"
          ? "custom"
          : cursorType === "hover"
          ? "hover"
          : "pointer"
      }`}
      style={{ left: `${mousePosition.x}px`, top: `${mousePosition.y}px` }}
    >
      <RiArrowRightLine />
    </div>
  );
});

export default CustomCursor;
