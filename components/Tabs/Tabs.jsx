import React, { useState } from 'react';
import PropTypes from 'prop-types';
import sectionsContentData from '../../components/Overlay/SectionDetails/sectionContentData.jsx'; // Ensure the path is correct

const Tabs = () => {
  const [activeTab, setActiveTab] = useState(sectionsContentData[0].key);

  return (
    <div className="tabs-container">
      {/* Tab Headers */}
      <div className="tabs-header">
        {sectionsContentData.map((section) => (
          <button
            key={section.key}
            className={`tab-button ${activeTab === section.key ? 'active' : ''}`}
            onClick={() => setActiveTab(section.key)}
          >
            {section.key}
          </button>
        ))}
      </div>

      {/* Active Tab Content */}
      <div className="tab-content">
        {sectionsContentData.find((section) => section.key === activeTab)?.content}
      </div>
    </div>
  );
};

Tabs.propTypes = {
  sectionsContentData: PropTypes.array.isRequired,
};

export default Tabs;
