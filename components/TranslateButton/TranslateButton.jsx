// TranslateButton.jsx

import React, { useState, useEffect, useRef } from "react";
import { ReactMic } from "react-mic";
import axios from "axios";
import { useAuth } from "../../AuthContext.jsx"; // Adjust the import path based on your project structure

const TranslateButton = ({ onSend, disabled }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const recorderTimeout = useRef(null);

  const { user } = useAuth(); // Access user data from AuthContext

  useEffect(() => {
    return () => {
      if (recorderTimeout.current) {
        clearTimeout(recorderTimeout.current);
      }
    };
  }, []);

  const startRecording = () => {
    setIsRecording(true);
    setIsListening(true);

    if (recorderTimeout.current) {
      clearTimeout(recorderTimeout.current);
    }
  };

  const stopRecording = () => {
    setIsRecording(false);
    setIsListening(false);

    if (recorderTimeout.current) {
      clearTimeout(recorderTimeout.current);
    }
  };

  const onData = () => {
    if (recorderTimeout.current) {
      clearTimeout(recorderTimeout.current);
    }

    recorderTimeout.current = setTimeout(() => {
      setIsRecording(false);
    }, 2000); // Stop recording after 2 seconds if no new data
  };

  
  const onStop = async (recordedBlob) => {
    const formData = new FormData();
    formData.append("file", recordedBlob.blob, "audio.mp3");

    // Get the token from localStorage or AuthContext
    const token = localStorage.getItem('token'); // or const { token } = useAuth();

    if (!token) {
      console.error('No authentication token found. Please log in.');
      return;
    }

    try {
      const response = await axios.post("https://ia.hexadecaedre.com:8443/translate-audio", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
          "Authorization": `Bearer ${token}`,
        },
      });

      const transcribedText = response.data.text;
      console.log("Transcribed Text:", transcribedText);
      onSend(transcribedText);

      if (isListening) {
        startRecording(); // Restart recording if `isListening` is true
      }
    } catch (error) {
      console.error("Error transcribing audio: ", error);
    }
  };

  return (
    <div className="translate-button-container">
      <ReactMic
        record={isRecording}
        onStop={onStop}
        onData={onData}
        className="hidden"
        style={{
          display: "none",
          width: 0,
          height: 0,
          overflow: "hidden",
        }}
      />
      <button
        className={`translate-button ${isListening ? "active" : ""}`}
        onClick={isListening ? stopRecording : startRecording}
        disabled={disabled}
        aria-pressed={isListening}
      >
        {isListening ? (
          // Active Icon
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 512 512"
            fill="currentColor"
            stroke="currentColor"
            className="translate-icon"
          >
            <path d="M468.53 236.03H486v39.94h-17.47v-39.94zm-34.426 51.634h17.47v-63.328h-17.47v63.328zm-33.848 32.756h17.47V191.58h-17.47v128.84zm-32.177 25.276h17.47V167.483h-17.47v178.17zm-34.448-43.521h17.47v-92.35h-17.47v92.35zm-34.994 69.879h17.47v-236.06h-17.525v236.06zM264.2 405.9h17.47V106.1H264.2V405.9zm-33.848-46.284h17.47V152.383h-17.47v207.234zm-35.016-58.85h17.47v-87.35h-17.47v87.35zm-33.847-20.823h17.47V231.98h-17.47v48.042zm-33.848 25.66h17.47v-99.24h-17.47v99.272zm-33.302 48.04h17.47V152.678H94.34v201zm-33.847-30.702h17.47V187.333h-17.47v135.642zM26 287.664h17.47v-63.328H26v63.328z" />
          </svg>
        ) : (
          // Inactive Icon
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="translate-icon"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 18.75a6 6 0 0 0 6-6v-1.5m-6 7.5a6 6 0 0 1-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 0 1-3-3V4.5a3 3 0 1 1 6 0v8.25a3 3 0 0 1-3 3Z"
            />
          </svg>
        )}
      </button>
    </div>
  );
};

export default TranslateButton;
