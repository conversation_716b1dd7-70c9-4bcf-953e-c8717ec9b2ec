import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../AuthContext';

const ProtectedRoute = ({ component: Component }) => {
  const { user } = useAuth();

  // Si l'utilisateur est connecté, afficher le composant
  if (user) {
    return <Component />;
  }

  // Sinon, rediriger vers la page de connexion
  return <Navigate to="/login" replace />;
};

export default ProtectedRoute;
