import React, { useEffect, useRef, useState } from 'react';
import { Canvas } from '@react-three/fiber';
import * as THREE from 'three';

const VideoTexture = ({ videoId }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const videoRef = useRef();
  const textureRef = useRef();
  const previewTexture = useRef();

  useEffect(() => {
    // Création de la vidéo
    const video = document.createElement('video');
    video.src = `https://www.youtube.com/embed/${videoId}?autoplay=0&mute=1&controls=0`;
    video.crossOrigin = 'anonymous';
    video.playsInline = true;
    video.muted = true; // Nécessaire pour autoplay
    video.loop = true;
    videoRef.current = video;

    // Préparer la texture d'aperçu
    const img = new Image();
    img.src = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`; // Aperçu YouTube
    img.crossOrigin = 'anonymous';
    img.onload = () => {
      const preview = new THREE.Texture(img);
      preview.needsUpdate = true;
      preview.minFilter = THREE.LinearFilter;
      preview.magFilter = THREE.LinearFilter;
      preview.format = THREE.RGBFormat;
      previewTexture.current = preview;
    };

    return () => {
      video.pause();
      video.remove();
    };
  }, [videoId]);

  // Gestion du clic pour démarrer la vidéo
  const handleClick = () => {
    if (videoRef.current && !isPlaying) {
      videoRef.current.play();
      const texture = new THREE.VideoTexture(videoRef.current);
      texture.minFilter = THREE.LinearFilter;
      texture.magFilter = THREE.LinearFilter;
      texture.format = THREE.RGBFormat;
      textureRef.current = texture;
      setIsPlaying(true);
    }
  };

  return (
    <mesh onClick={handleClick}>
      <planeGeometry args={[16, 9]} />
      <meshBasicMaterial
        map={isPlaying ? textureRef.current : previewTexture.current}
        toneMapped={false}
      />
    </mesh>
  );
};

const Youtube = () => {
  return (
    <Canvas>
      <ambientLight />
      <VideoTexture videoId="ifVVc8xTas0" /> {/* Remplacez par l'ID YouTube souhaité */}
    </Canvas>
  );
};

export default Youtube;
