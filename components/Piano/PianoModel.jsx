import React, { useRef, useEffect } from 'react'
import { useGLTF, useAnimations } from '@react-three/drei'
import * as THREE from 'three'

export function Model(props) {
  const group = useRef()
  const environmentBoundingBox = useRef(new THREE.Box3()) // La bounding box de l'environnement
  const characterBoundingBox = useRef(new THREE.Box3()) // La bounding box du personnage

  const { nodes, materials, animations } = useGLTF('models/piano.glb')
  const { actions } = useAnimations(animations, group)

  // Démarre l'animation par défaut
  useEffect(() => {
    if (actions) {
      console.log('Animations disponibles :', actions) // Vérifiez les animations disponibles dans la console
      const defaultAction = actions[Object.keys(actions)[0]] // Récupère la première animation
      if (defaultAction) defaultAction.play() // Joue l'animation si elle existe
    }
  }, [actions])


  return (
    <group ref={group} {...props} dispose={null}>
      <group name="Scene">
        <mesh
          name="Object_11001"
          castShadow
          receiveShadow
          geometry={nodes.Object_11001.geometry}
          material={materials.Ch07_hair}
        />
        <group
          name="Armature"
          position={[0.677, 5.412, 32.224]}
          rotation={[Math.PI / 2, 0, 3.072]}
          scale={0.01}>
          <skinnedMesh
            name="Icosphere001"
            geometry={nodes.Icosphere001.geometry}
            material={nodes.Icosphere001.material}
            skeleton={nodes.Icosphere001.skeleton}
          />
          <skinnedMesh
            name="Object_11002"
            geometry={nodes.Object_11002.geometry}
            material={materials['Ch07_hair.001']}
            skeleton={nodes.Object_11002.skeleton}
          />
          <skinnedMesh
            name="Object_12002"
            geometry={nodes.Object_12002.geometry}
            material={materials['Ch07_body.001']}
            skeleton={nodes.Object_12002.skeleton}
          />
          <skinnedMesh
            name="Object_13002"
            geometry={nodes.Object_13002.geometry}
            material={materials['Ch07_body.001']}
            skeleton={nodes.Object_13002.skeleton}
          />
          <skinnedMesh
            name="Object_14002"
            geometry={nodes.Object_14002.geometry}
            material={materials['Ch07_body.001']}
            skeleton={nodes.Object_14002.skeleton}
          />
          <skinnedMesh
            name="Object_16002"
            geometry={nodes.Object_16002.geometry}
            material={materials['Ch07_hair.001']}
            skeleton={nodes.Object_16002.skeleton}
          />
          <skinnedMesh
            name="Object_7002"
            geometry={nodes.Object_7002.geometry}
            material={materials['Ch07_body.001']}
            skeleton={nodes.Object_7002.skeleton}
          />
          <skinnedMesh
            name="Object_9002"
            geometry={nodes.Object_9002.geometry}
            material={materials['Ch07_body.001']}
            skeleton={nodes.Object_9002.skeleton}
          />
          <primitive object={nodes.mixamorigHips} />
        </group>
        <group name="Sketchfab_model" rotation={[-Math.PI / 2, 0, 0]} />
        <group
          name="Sketchfab_model001"
          position={[-19, -2, -96]}
          rotation={[-Math.PI / 2, 0, 0]}
          scale={100}>
          <group name="root">
            <group name="GLTF_SceneRootNode" rotation={[Math.PI / 2, 0, 0]}>
              <group name="Cube002_47" position={[0, 0.509, 1.355]} scale={[0.429, 0.041, 0.27]}>
                <mesh
                  name="Object_99"
                  castShadow
                  receiveShadow
                  geometry={nodes.Object_99.geometry}
                  material={materials['Material.009']}
                />
              </group>
              <group name="Cube_46" position={[0, 0.509, 1.355]} scale={[0.429, 0.041, 0.27]}>
                <mesh
                  name="Object_97"
                  castShadow
                  receiveShadow
                  geometry={nodes.Object_97.geometry}
                  material={materials['Material.005']}
                />
              </group>
              <group name="piano_44" position={[0, 0.922, 0]}>
                <group name="Cube001_1" position={[-0.874, 0.307, 0.71]} rotation={[0, 0, 0.643]}>
                  <mesh
                    name="Object_7001"
                    castShadow
                    receiveShadow
                    geometry={nodes.Object_7001.geometry}
                    material={materials['Material.001']}
                  />
                </group>
                <mesh
                  name="Object_4001"
                  castShadow
                  receiveShadow
                  geometry={nodes.Object_4001.geometry}
                  material={materials['Material.001']}
                />
                <mesh
                  name="Object_5001"
                  castShadow
                  receiveShadow
                  geometry={nodes.Object_5001.geometry}
                  material={materials['Material.004']}
                />
                <group
                  name="Plane037_39"
                  position={[0.718, 0.244, 0.118]}
                  rotation={[0, 0, -0.914]}
                  scale={0.055}>
                  <mesh
                    name="Object_83"
                    castShadow
                    receiveShadow
                    geometry={nodes.Object_83.geometry}
                    material={materials['Material.001']}
                  />
                </group>
                <group name="Plane038_41" position={[-0.297, -0.038, -1.441]}>
                  <group
                    name="Cylinder001_40"
                    position={[-0.434, -0.84, 1.931]}
                    rotation={[0, 0, -Math.PI / 2]}
                    scale={[0.049, 0.017, 0.049]}>
                    <mesh
                      name="Object_87"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_87.geometry}
                      material={materials['Material.007']}
                    />
                  </group>
                  <mesh
                    name="Object_85"
                    castShadow
                    receiveShadow
                    geometry={nodes.Object_85.geometry}
                    material={materials['Material.001']}
                  />
                </group>
                <group
                  name="Plane039_42"
                  position={[0, -0.159, 0.468]}
                  scale={[0.172, 0.159, 0.172]}>
                  <mesh
                    name="Object_89"
                    castShadow
                    receiveShadow
                    geometry={nodes.Object_89.geometry}
                    material={materials['Material.001']}
                  />
                  <mesh
                    name="Object_90"
                    castShadow
                    receiveShadow
                    geometry={nodes.Object_90.geometry}
                    material={materials['Material.007']}
                  />
                </group>
                <group
                  name="Plane042_43"
                  position={[-0.343, 0.187, -1.422]}
                  rotation={[Math.PI, -1.347, Math.PI / 2]}
                  scale={[0.049, 0.92, 0.213]}>
                  <mesh
                    name="Object_92"
                    castShadow
                    receiveShadow
                    geometry={nodes.Object_92.geometry}
                    material={materials['Material.003']}
                  />
                  <mesh
                    name="Object_93"
                    castShadow
                    receiveShadow
                    geometry={nodes.Object_93.geometry}
                    material={materials['Material.006']}
                  />
                </group>
                <group name="Plane_38" position={[-0.723, -0.074, 0.842]} scale={0.012}>
                  <mesh
                    name="Object_9001"
                    castShadow
                    receiveShadow
                    geometry={nodes.Object_9001.geometry}
                    material={materials['Material.010']}
                  />
                  <group name="Plane001_2" position={[1.353, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_11003"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_11003.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane002_3" position={[6.069, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_13001"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_13001.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane003_4" position={[8.544, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_15001"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_15001.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane004_5" position={[13.209, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_17001"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_17001.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane005_6" position={[15.67, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_19001"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_19001.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane006_7" position={[17.966, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_21"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_21.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane007_8" position={[22.717, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_23"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_23.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane008_9" position={[25.192, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_25"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_25.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane009_10" position={[29.857, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_27"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_27.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane010_11" position={[32.318, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_29"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_29.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane011_12" position={[34.614, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_31"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_31.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane012_13" position={[39.486, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_33"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_33.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane013_14" position={[41.961, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_35"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_35.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane014_15" position={[46.626, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_37"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_37.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane015_16" position={[49.087, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_39"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_39.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane016_17" position={[51.383, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_41"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_41.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane017_18" position={[56.142, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_43"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_43.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane018_19" position={[58.618, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_45"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_45.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane019_20" position={[63.282, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_47"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_47.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane020_21" position={[65.743, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_49"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_49.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane021_22" position={[68.039, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_51"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_51.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane022_23" position={[72.951, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_53"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_53.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane023_24" position={[75.427, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_55"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_55.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane024_25" position={[80.091, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_57"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_57.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane025_26" position={[82.552, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_59"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_59.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane026_27" position={[84.849, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_61"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_61.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane027_28" position={[89.579, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_63"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_63.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane028_29" position={[92.055, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_65"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_65.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane029_30" position={[96.719, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_67"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_67.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane030_31" position={[99.18, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_69"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_69.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane031_32" position={[101.477, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_71"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_71.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane032_33" position={[106.328, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_73"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_73.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane033_34" position={[108.803, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_75"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_75.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane034_35" position={[113.468, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_77"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_77.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane035_36" position={[115.929, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_79"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_79.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                  <group name="Plane036_37" position={[118.225, -0.007, -3.588]} scale={0.773}>
                    <mesh
                      name="Object_81"
                      castShadow
                      receiveShadow
                      geometry={nodes.Object_81.geometry}
                      material={materials['Material.002']}
                    />
                  </group>
                </group>
              </group>
              <group name="Plane040_45" scale={100} />
            </group>
          </group>
        </group>
      </group>
    </group>
  )
}
 

useGLTF.preload('models/piano.glb')
