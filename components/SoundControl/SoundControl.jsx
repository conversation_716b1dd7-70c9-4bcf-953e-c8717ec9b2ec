// components/SoundControl/SoundControl.js
import React from "react";
import { useStore } from "../../store/store"; // Ensure correct path to the store

const SoundControl = () => {
  const [soundLevel, setSoundLevel] = useStore((state) => [
    state.soundLevel,
    state.setSoundLevel,
  ]);

  return (
    <div className="control">
      <input
        type="range"
        className="volume-slider"
        min="0"
        max="100"
        value={soundLevel}
        onChange={(e) => setSoundLevel(parseInt(e.target.value, 10))}
        aria-label="Volume control slider"
      />
    </div>
  );
};

export default SoundControl;
